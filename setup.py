#!/usr/bin/env python3
"""
Quick Setup Script for PropBolt API Key Management
Uses your existing database configuration
"""

import os
import sys
import subprocess

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result"""
    print(f"🔧 Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"❌ Command failed: {command}")
        print(f"Error: {result.stderr}")
        if not check:
            return result
        sys.exit(1)
    
    if result.stdout:
        print(result.stdout)
    
    return result

def main():
    """Main setup function"""
    print("🚀 PropBolt API Key Management - Quick Setup")
    print("=" * 60)
    print("Framework: FastAPI (not Flask)")
    print("Port: App Engine Default (Google Cloud manages)")
    print("Database Configuration (from gcloud sql):")
    print("  Host: *************:5432")
    print("  Database: propbolt")
    print("  User: propbolt_user")
    print("  SSL: Prefer (flexible encryption)")
    print()
    
    # Step 1: Install dependencies
    print("📦 Step 1: Installing Python dependencies...")
    try:
        run_command("pip install -r requirements.txt")
        print("✅ Dependencies installed successfully")
    except:
        print("⚠️  Some dependencies may have failed to install")
        print("   This is normal if some packages are already installed")
    
    print()
    
    # Step 2: Test database connection
    print("🔍 Step 2: Testing database connection...")
    try:
        # Set environment variables for testing (Google Cloud Production PostgreSQL)
        os.environ["DB_HOST"] = "*************"
        os.environ["DB_PORT"] = "5432"
        os.environ["DB_NAME"] = "propbolt"
        os.environ["DB_USER"] = "propbolt_user"
        os.environ["DB_PASSWORD"] = "PropBolt2024!"
        os.environ["DB_SSL_MODE"] = "prefer"
        os.environ["CLOUD_SQL_CONNECTION_NAME"] = "gold-braid-458901-v2:us-central1:propbolt-postgres"

        # Ensure we're using the production database
        print(f"   Connecting to: {os.environ['DB_HOST']}:{os.environ['DB_PORT']}")
        print(f"   Database: {os.environ['DB_NAME']}")
        print(f"   User: {os.environ['DB_USER']}")
        print(f"   SSL Mode: {os.environ['DB_SSL_MODE']}")
        
        # Test connection
        from database.connection import DatabaseManager
        db_manager = DatabaseManager()
        
        if db_manager.test_sync_connection():
            print("✅ Database connection successful")
        else:
            print("❌ Database connection failed")
            print("   Please check your database credentials and network connectivity")
            return False
            
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        print("   This might be due to missing dependencies or network issues")
        return False
    
    print()
    
    # Step 3: Run database migration
    print("📊 Step 3: Setting up database schema...")
    try:
        run_command("python database/migrations.py")
        print("✅ Database schema setup completed")
    except:
        print("❌ Database migration failed")
        print("   Please run 'python database/migrations.py' manually")
        return False
    
    print()
    
    # Step 4: Test the application locally
    print("🧪 Step 4: Testing application locally...")
    print("Starting local server for 10 seconds...")
    
    try:
        # Start server in background
        import threading
        import time
        import requests
        
        def start_server():
            os.system("python main.py &")
        
        server_thread = threading.Thread(target=start_server)
        server_thread.daemon = True
        server_thread.start()
        
        # Wait for server to start
        time.sleep(3)
        
        # Test health endpoint
        try:
            response = requests.get("http://localhost:8080/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print("✅ Local server test successful")
                print(f"   API Key Management: {health_data.get('api_key_management', 'unknown')}")
                print(f"   Database Status: {health_data.get('database', {}).get('overall', 'unknown')}")
            else:
                print(f"⚠️  Health check returned status {response.status_code}")
        except Exception as e:
            print(f"⚠️  Could not test local server: {e}")
        
        # Stop the server
        os.system("pkill -f 'python main.py'")
        
    except Exception as e:
        print(f"⚠️  Local server test failed: {e}")
    
    print()
    
    # Step 5: Ready for deployment
    print("🚀 Step 5: Ready for deployment!")
    print("=" * 40)
    print("✅ Dependencies installed")
    print("✅ Database connection verified")
    print("✅ Database schema created")
    print("✅ Application tested locally")
    print()
    print("🎯 Next Steps:")
    print("1. Deploy to Google Cloud (data.propbolt.com):")
    print("   gcloud app deploy app.yaml")
    print()
    print("2. Test deployed API:")
    print("   curl https://data.propbolt.com/health")
    print()
    print("3. Access admin interface:")
    print("   https://data.propbolt.com/admin/docs")
    print("   Admin Key: admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    print()
    print("4. Create API keys:")
    print("   Use the admin interface to create database-managed keys")
    print()
    print("🔑 Current API Keys:")
    print("   Fallback AYO Key: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    print("   (This key will continue to work for backward compatibility)")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 Setup completed successfully!")
        else:
            print("\n❌ Setup completed with issues")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
