#!/usr/bin/env python3
"""
Comprehensive Endpoint Testing for PropBolt API
Tests all 13 endpoints before deployment to data.propbolt.com
"""

import os
import sys
import time
import json
import requests
import subprocess
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime

# Test configuration
API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
ADMIN_API_KEY = "admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
BASE_URL = "http://localhost:8080"
SERVER_STARTUP_WAIT = 5  # seconds to wait for server startup

class EndpointTester:
    """Comprehensive endpoint testing class"""
    
    def __init__(self, base_url: str, api_key: str, admin_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.admin_key = admin_key
        self.session = requests.Session()
        self.results = []
        
    def log_result(self, endpoint: str, method: str, status: str, details: str = ""):
        """Log test result"""
        result = {
            "endpoint": endpoint,
            "method": method,
            "status": status,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.results.append(result)
        
        # Color coding for console output
        if status == "PASS":
            print(f"✅ {method} {endpoint} - {status}")
        elif status == "FAIL":
            print(f"❌ {method} {endpoint} - {status}: {details}")
        elif status == "SKIP":
            print(f"⏭️  {method} {endpoint} - {status}: {details}")
        else:
            print(f"⚠️  {method} {endpoint} - {status}: {details}")
            
        if details:
            print(f"   Details: {details}")
    
    def test_health_endpoint(self):
        """Test health check endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_result("/health", "GET", "PASS", 
                              f"API Key Management: {data.get('api_key_management', 'unknown')}")
                return True
            else:
                self.log_result("/health", "GET", "FAIL", 
                              f"Status code: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("/health", "GET", "FAIL", str(e))
            return False
    
    def test_property_search(self):
        """Test PropertySearch endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210",
            "radius": 1.0,
            "limit": 10
        }
        return self._test_post_endpoint("/v2/PropertySearch", payload)
    
    def test_property_detail(self):
        """Test PropertyDetail endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210"
        }
        return self._test_post_endpoint("/v2/PropertyDetail", payload)
    
    def test_property_detail_bulk(self):
        """Test PropertyDetailBulk endpoint"""
        payload = {
            "addresses": [
                "123 Main St, Anytown, CA 90210",
                "456 Oak Ave, Somewhere, TX 75001"
            ]
        }
        return self._test_post_endpoint("/v2/PropertyDetailBulk", payload)
    
    def test_property_parcel(self):
        """Test PropertyParcel endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210"
        }
        return self._test_post_endpoint("/v1/PropertyParcel", payload)
    
    def test_property_comps_v2(self):
        """Test PropertyComps v2 endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210",
            "radius": 0.5,
            "limit": 5
        }
        return self._test_post_endpoint("/v2/PropertyComps", payload)
    
    def test_property_comps_v3(self):
        """Test PropertyComps v3 endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210",
            "radius": 0.5,
            "limit": 5
        }
        return self._test_post_endpoint("/v3/PropertyComps", payload)
    
    def test_autocomplete(self):
        """Test AutoComplete endpoint"""
        payload = {
            "query": "123 Main St"
        }
        return self._test_post_endpoint("/v2/AutoComplete", payload)
    
    def test_address_verification(self):
        """Test AddressVerification endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210"
        }
        return self._test_post_endpoint("/v2/AddressVerification", payload)
    
    def test_prop_gpt(self):
        """Test PropGPT endpoint"""
        payload = {
            "query": "What is the property value of 123 Main St?",
            "address": "123 Main St, Anytown, CA 90210"
        }
        return self._test_post_endpoint("/v2/PropGPT", payload)
    
    def test_csv_builder(self):
        """Test CSVBuilder endpoint"""
        payload = {
            "addresses": [
                "123 Main St, Anytown, CA 90210",
                "456 Oak Ave, Somewhere, TX 75001"
            ],
            "fields": ["address", "value", "bedrooms", "bathrooms"]
        }
        return self._test_post_endpoint("/v2/CSVBuilder", payload)
    
    def test_property_avm(self):
        """Test PropertyAvm endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210"
        }
        return self._test_post_endpoint("/v2/PropertyAvm", payload)
    
    def test_property_liens(self):
        """Test PropertyLiens endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210"
        }
        return self._test_post_endpoint("/v2/Reports/PropertyLiens", payload)
    
    def test_property_mapping(self):
        """Test PropertyMapping endpoint"""
        payload = {
            "address": "123 Main St, Anytown, CA 90210",
            "radius": 1.0
        }
        return self._test_post_endpoint("/v2/PropertyMapping", payload)
    
    def test_admin_health(self):
        """Test admin health endpoint"""
        try:
            headers = {"x-admin-key": self.admin_key}
            response = self.session.get(f"{self.base_url}/admin/health", 
                                      headers=headers, timeout=10)
            if response.status_code == 200:
                self.log_result("/admin/health", "GET", "PASS", "Admin interface accessible")
                return True
            else:
                self.log_result("/admin/health", "GET", "FAIL", 
                              f"Status code: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("/admin/health", "GET", "FAIL", str(e))
            return False
    
    def test_admin_api_keys_list(self):
        """Test admin API keys list endpoint"""
        try:
            headers = {"x-admin-key": self.admin_key}
            response = self.session.get(f"{self.base_url}/admin/api-keys", 
                                      headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                self.log_result("/admin/api-keys", "GET", "PASS", 
                              f"Found {len(data)} API keys")
                return True
            else:
                self.log_result("/admin/api-keys", "GET", "FAIL", 
                              f"Status code: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("/admin/api-keys", "GET", "FAIL", str(e))
            return False
    
    def _test_post_endpoint(self, endpoint: str, payload: Dict[Any, Any]) -> bool:
        """Generic POST endpoint tester"""
        try:
            headers = {
                "x-api-key": self.api_key,
                "Content-Type": "application/json"
            }
            response = self.session.post(
                f"{self.base_url}{endpoint}",
                json=payload,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                self.log_result(endpoint, "POST", "PASS", 
                              f"Response size: {len(response.content)} bytes")
                return True
            elif response.status_code == 401:
                self.log_result(endpoint, "POST", "FAIL", "Authentication failed")
                return False
            elif response.status_code == 429:
                self.log_result(endpoint, "POST", "FAIL", "Rate limit exceeded")
                return False
            else:
                self.log_result(endpoint, "POST", "FAIL", 
                              f"Status code: {response.status_code}")
                return False
                
        except requests.exceptions.Timeout:
            self.log_result(endpoint, "POST", "FAIL", "Request timeout")
            return False
        except Exception as e:
            self.log_result(endpoint, "POST", "FAIL", str(e))
            return False
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all endpoint tests"""
        print("🧪 Starting comprehensive endpoint testing...")
        print("=" * 60)
        
        # Test basic endpoints first
        health_ok = self.test_health_endpoint()
        if not health_ok:
            print("❌ Health check failed - aborting tests")
            return self._generate_report()
        
        # Test all 13 main API endpoints
        print("\n📋 Testing Main API Endpoints (13 total):")
        print("-" * 40)
        
        test_methods = [
            self.test_property_search,
            self.test_property_detail,
            self.test_property_detail_bulk,
            self.test_property_parcel,
            self.test_property_comps_v2,
            self.test_property_comps_v3,
            self.test_autocomplete,
            self.test_address_verification,
            self.test_prop_gpt,
            self.test_csv_builder,
            self.test_property_avm,
            self.test_property_liens,
            self.test_property_mapping
        ]
        
        for test_method in test_methods:
            test_method()
            time.sleep(0.5)  # Small delay between tests
        
        # Test admin endpoints
        print("\n🔧 Testing Admin Endpoints:")
        print("-" * 30)
        self.test_admin_health()
        self.test_admin_api_keys_list()
        
        return self._generate_report()
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate test report"""
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        
        report = {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "results": self.results
        }
        
        print("\n" + "=" * 60)
        print("📊 TEST REPORT SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {report['success_rate']:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.results:
                if result["status"] == "FAIL":
                    print(f"   - {result['method']} {result['endpoint']}: {result['details']}")
        
        return report


def start_local_server():
    """Start the local server for testing"""
    print("🚀 Starting local server...")
    
    # Use sudo to start the server
    cmd = ["sudo", "python", "main.py"]
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    print(f"⏳ Waiting {SERVER_STARTUP_WAIT} seconds for server startup...")
    time.sleep(SERVER_STARTUP_WAIT)
    
    return process


def stop_local_server(process):
    """Stop the local server"""
    print("⏹️  Stopping local server...")
    try:
        # Use sudo to kill the process
        subprocess.run(["sudo", "pkill", "-f", "python main.py"], check=False)
        process.terminate()
        process.wait(timeout=5)
    except subprocess.TimeoutExpired:
        subprocess.run(["sudo", "kill", "-9", str(process.pid)], check=False)
    except Exception as e:
        print(f"⚠️  Error stopping server: {e}")


def main():
    """Main testing function"""
    print("🧪 PropBolt API Comprehensive Endpoint Testing")
    print("=" * 60)
    print("This will test all 13 endpoints + admin interface")
    print("Using sudo for server management")
    print()
    
    # Check if we can use sudo
    try:
        subprocess.run(["sudo", "-n", "true"], check=True, capture_output=True)
        print("✅ Sudo access confirmed")
    except subprocess.CalledProcessError:
        print("❌ Sudo access required. Please run with sudo privileges.")
        print("Run: sudo python test_endpoints.py")
        sys.exit(1)
    
    server_process = None
    
    try:
        # Start local server
        server_process = start_local_server()
        
        # Initialize tester
        tester = EndpointTester(BASE_URL, API_KEY, ADMIN_API_KEY)
        
        # Run all tests
        report = tester.run_all_tests()
        
        # Save report
        with open("test_report.json", "w") as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: test_report.json")
        
        # Determine if ready for deployment
        if report["success_rate"] >= 80:
            print("\n🎉 READY FOR DEPLOYMENT!")
            print("✅ Most endpoints are working correctly")
            print("\nNext steps:")
            print("1. sudo gcloud app deploy app.yaml")
            print("2. Test deployed endpoints at data.propbolt.com")
        else:
            print("\n⚠️  NOT READY FOR DEPLOYMENT")
            print("❌ Too many endpoint failures")
            print("Please fix the failing endpoints before deploying")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Testing cancelled by user")
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
        sys.exit(1)
    finally:
        if server_process:
            stop_local_server(server_process)


if __name__ == "__main__":
    main()
