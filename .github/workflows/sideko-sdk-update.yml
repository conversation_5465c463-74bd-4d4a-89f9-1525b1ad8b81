name: Update SDK with <PERSON><PERSON>

on:
  # Trigger when the OpenAPI spec changes
  push:
    paths:
      - 'propbolt-0.1.0.yaml'
      - 'new-openapi.yaml'
      - 'sdk-config.yaml'
    branches:
      - main
  
  # Allow manual triggering
  workflow_dispatch:
  
  # Trigger on schedule (daily check)
  schedule:
    - cron: '0 6 * * *'  # Run daily at 6 AM UTC

jobs:
  update-sdk:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0
      
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.8'
      
      - name: Install Sideko CLI
        run: |
          pip install sideko-py
      
      - name: Configure Sideko CLI
        env:
          SIDEKO_API_KEY: ${{ secrets.SIDEKO_API_KEY }}
        run: |
          # Set up Sideko authentication
          echo "Setting up Sideko CLI authentication..."
          # The CLI will use the SIDEKO_API_KEY environment variable
      
      - name: Generate SDK
        env:
          SIDEKO_API_KEY: ${{ secrets.SIDEKO_API_KEY }}
        run: |
          echo "Generating SDK with Sideko..."
          sideko sdk generate --config sdk-config.yaml
      
      - name: Check for changes
        id: changes
        run: |
          if git diff --quiet; then
            echo "No changes detected"
            echo "has_changes=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected"
            echo "has_changes=true" >> $GITHUB_OUTPUT
          fi
      
      - name: Commit and push changes
        if: steps.changes.outputs.has_changes == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add .
          git commit -m "🤖 Auto-update SDK via Sideko"
          git push
      
      - name: Create Pull Request
        if: steps.changes.outputs.has_changes == 'true'
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "🤖 Auto-update SDK via Sideko"
          title: "Auto-update SDK via Sideko"
          body: |
            This PR was automatically created by the Sideko GitHub Action.
            
            ## Changes
            - SDK updated based on latest OpenAPI specification
            - Generated using Sideko CLI
            
            ## Review
            Please review the changes and merge if everything looks correct.
          branch: sideko-sdk-update
          delete-branch: true
