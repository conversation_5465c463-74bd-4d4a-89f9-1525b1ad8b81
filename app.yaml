runtime: python39
service: data-propbolt-api

# Use Secret Manager for sensitive data (recommended)
# Uncomment and use this instead of env_variables for production
# automatic_scaling:
#   min_instances: 1
#   max_instances: 10
#   target_cpu_utilization: 0.6

env_variables:
  # API Key Configuration (maintains backward compatibility)
  API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  REAL_ESTATE_API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  ENVIRONMENT: "PRODUCTION"
  # PORT is automatically set by App Engine

  # Database Configuration for API Key Management (from gcloud sql)
  DB_HOST: "*************"
  DB_PORT: "5432"
  DB_NAME: "propbolt"
  DB_USER: "propbolt_user"
  DB_PASSWORD: "PropBolt2024!"
  DB_SSL_MODE: "prefer"
  DATABASE_URL: "***********************************************************/propbolt?sslmode=prefer"

  # Cloud SQL Connection (alternative for App Engine)
  CLOUD_SQL_CONNECTION_NAME: "gold-braid-458901-v2:us-central1:propbolt-postgres"

  # Google Cloud Configuration
  GCP_PROJECT_ID: "gold-braid-458901-v2"
  GCP_PROJECT_NUMBER: "456078002475"
  GCP_REGION: "us-central1"

  # Database Connection Pool Settings
  DB_POOL_SIZE: "5"
  DB_MAX_OVERFLOW: "10"
  DB_POOL_TIMEOUT: "30"
  DB_POOL_RECYCLE: "3600"

  # Admin API Key for management interface
  ADMIN_API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"

automatic_scaling:
  min_instances: 1
  max_instances: 10
  target_cpu_utilization: 0.6

handlers:
- url: /.*
  script: auto

entrypoint: uvicorn main:app --host 0.0.0.0 --port $PORT
