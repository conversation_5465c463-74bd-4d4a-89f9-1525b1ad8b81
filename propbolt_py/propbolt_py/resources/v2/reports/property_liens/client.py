import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
    type_utils,
)
from propbolt_py.types import models, params


class PropertyLiensClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        apn: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        fips: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        zip: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2ReportsPropertyLiensCreateResponse:
        """
        Involuntary Liens API

        Go beyond standard tax liens and add involuntary lien data to your property insights.
        Includes federal tax liens, judgment liens, mechanic's liens, and other encumbrances.


        POST /v2/Reports/PropertyLiens

        Args:
            address: Fully formatted address
            apn: Assessor's Parcel Number
            fips: FIPS county code
            id: Property ID from search results
            zip: 5-digit ZIP code
            request_options: Additional options to customize the HTTP request

        Returns:
            Property lien information retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v2.reports.property_liens.create(
            address="123 Main St, Arlington, VA 22205"
        )
        ```
        """
        _json = to_encodable(
            item={"address": address, "apn": apn, "fips": fips, "id": id, "zip": zip},
            dump_with=params._SerializerV2ReportsPropertyLiensCreateBody,
        )
        return self._base_client.request(
            method="POST",
            path="/v2/Reports/PropertyLiens",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2ReportsPropertyLiensCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncPropertyLiensClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        apn: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        fips: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        zip: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2ReportsPropertyLiensCreateResponse:
        """
        Involuntary Liens API

        Go beyond standard tax liens and add involuntary lien data to your property insights.
        Includes federal tax liens, judgment liens, mechanic's liens, and other encumbrances.


        POST /v2/Reports/PropertyLiens

        Args:
            address: Fully formatted address
            apn: Assessor's Parcel Number
            fips: FIPS county code
            id: Property ID from search results
            zip: 5-digit ZIP code
            request_options: Additional options to customize the HTTP request

        Returns:
            Property lien information retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v2.reports.property_liens.create(
            address="123 Main St, Arlington, VA 22205"
        )
        ```
        """
        _json = to_encodable(
            item={"address": address, "apn": apn, "fips": fips, "id": id, "zip": zip},
            dump_with=params._SerializerV2ReportsPropertyLiensCreateBody,
        )
        return await self._base_client.request(
            method="POST",
            path="/v2/Reports/PropertyLiens",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2ReportsPropertyLiensCreateResponse,
            request_options=request_options or default_request_options(),
        )
