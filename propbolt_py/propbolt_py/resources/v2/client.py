from propbolt_py.core import AsyncBase<PERSON>lient, SyncBaseClient
from propbolt_py.resources.v2.address_verification import (
    AddressVerificationClient,
    AsyncAddressVerificationClient,
)
from propbolt_py.resources.v2.auto_complete import (
    AsyncAutoCompleteClient,
    AutoCompleteClient,
)
from propbolt_py.resources.v2.csv_builder import AsyncCsvBuilderClient, CsvBuilderClient
from propbolt_py.resources.v2.prop_gpt import AsyncPropGptClient, PropGptClient
from propbolt_py.resources.v2.property_avm import (
    AsyncPropertyAvmClient,
    PropertyAvmClient,
)
from propbolt_py.resources.v2.property_comps import (
    AsyncPropertyCompsClient,
    PropertyCompsClient,
)
from propbolt_py.resources.v2.property_detail import (
    AsyncPropertyDetailClient,
    PropertyDetailClient,
)
from propbolt_py.resources.v2.property_detail_bulk import (
    AsyncPropertyDetailBulkClient,
    PropertyDetailBulkClient,
)
from propbolt_py.resources.v2.property_mapping import (
    Async<PERSON>ropertyMappingClient,
    PropertyMappingClient,
)
from propbolt_py.resources.v2.property_search import (
    AsyncPropertySearchClient,
    PropertySearchClient,
)
from propbolt_py.resources.v2.reports import AsyncReportsClient, ReportsClient


class V2Client:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client
        self.address_verification = AddressVerificationClient(
            base_client=self._base_client
        )
        self.auto_complete = AutoCompleteClient(base_client=self._base_client)
        self.csv_builder = CsvBuilderClient(base_client=self._base_client)
        self.prop_gpt = PropGptClient(base_client=self._base_client)
        self.property_avm = PropertyAvmClient(base_client=self._base_client)
        self.property_comps = PropertyCompsClient(base_client=self._base_client)
        self.property_detail = PropertyDetailClient(base_client=self._base_client)
        self.property_detail_bulk = PropertyDetailBulkClient(
            base_client=self._base_client
        )
        self.property_mapping = PropertyMappingClient(base_client=self._base_client)
        self.property_search = PropertySearchClient(base_client=self._base_client)
        self.reports = ReportsClient(base_client=self._base_client)


class AsyncV2Client:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client
        self.address_verification = AsyncAddressVerificationClient(
            base_client=self._base_client
        )
        self.auto_complete = AsyncAutoCompleteClient(base_client=self._base_client)
        self.csv_builder = AsyncCsvBuilderClient(base_client=self._base_client)
        self.prop_gpt = AsyncPropGptClient(base_client=self._base_client)
        self.property_avm = AsyncPropertyAvmClient(base_client=self._base_client)
        self.property_comps = AsyncPropertyCompsClient(base_client=self._base_client)
        self.property_detail = AsyncPropertyDetailClient(base_client=self._base_client)
        self.property_detail_bulk = AsyncPropertyDetailBulkClient(
            base_client=self._base_client
        )
        self.property_mapping = AsyncPropertyMappingClient(
            base_client=self._base_client
        )
        self.property_search = AsyncPropertySearchClient(base_client=self._base_client)
        self.reports = AsyncReportsClient(base_client=self._base_client)
