
### Property Comparables API v2 <a name="create"></a>

Generate property comparables (comps) for valuation analysis using our standard algorithm.


**API Endpoint**: `POST /v2/PropertyComps`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.property_comps.create()

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.property_comps.create()

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `address` | ✗ | Fully formatted address | `"string"` |
| `id` | ✗ | Property ID | `"string"` |
