
### Property Detail API <a name="create"></a>

Comprehensive property information including comps, mortgages, mailing addresses, 
property sales history & more!


**API Endpoint**: `POST /v2/PropertyDetail`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.property_detail.create()

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.property_detail.create()

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `address` | ✗ | Fully formatted address | `"string"` |
| `apn` | ✗ | Assessor's Parcel Number | `"string"` |
| `city` | ✗ | City name | `"string"` |
| `comps` | ✗ | Include comparable properties | `True` |
| `county` | ✗ | County name | `"string"` |
| `exact_match` | ✗ | Require exact address match | `True` |
| `fips` | ✗ | FIPS county code | `"string"` |
| `house` | ✗ | House number | `"string"` |
| `id` | ✗ | Property ID from search results | `"string"` |
| `state` | ✗ | Two-letter state code | `"string"` |
| `street` | ✗ | Street name | `"string"` |
| `unit` | ✗ | Unit number | `"string"` |
| `zip` | ✗ | 5-digit ZIP code | `"string"` |
