
### PropGPT API <a name="create"></a>

Natural language property search using AI. Convert natural language queries 
into structured property searches. Check out the functionality at https://www.propgpt.com


**API Endpoint**: `POST /v2/PropGPT`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.prop_gpt.create(
    query="Find all properties listed for sale in Herndon Virginia between 600K and 700K",
    x_api_key="string",
    x_openai_key="string",
)

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.prop_gpt.create(
    query="Find all properties listed for sale in Herndon Virginia between 600K and 700K",
    x_api_key="string",
    x_openai_key="string",
)

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `query` | ✓ | Natural language string that references data points for property search | `"Find all properties listed for sale in Herndon Virginia between 600K and 700K"` |
| `x-api-key` | ✓ | Your Real Estate API key | `"string"` |
| `x-openai-key` | ✓ | Your OpenAI API key for token spend tracking | `"string"` |
| `model` | ✗ | OpenAI model to use for query processing | `"gpt-3.5-turbo"` |
| `size` | ✗ | Maximum number of results to return | `123` |
