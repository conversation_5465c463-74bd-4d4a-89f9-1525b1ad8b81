import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
)
from propbolt_py.types import models, params


class PropertyDetailBulkClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        ids: typing.List[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2PropertyDetailBulkCreateResponse:
        """
        Property Detail Bulk API

        For retrieving up to 1000 properties at once. Can be used standalone, but it's designed
        to work together with the Property Search API. Use this API for quickly exporting lists,
        or bulk search requests for analytics.


        POST /v2/PropertyDetailBulk

        Args:
            ids: List of property IDs (max 1000)
            request_options: Additional options to customize the HTTP request

        Returns:
            Bulk property details retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v2.property_detail_bulk.create(ids=["string"])
        ```
        """
        _json = to_encodable(
            item={"ids": ids}, dump_with=params._SerializerPropertyBulkParameters
        )
        return self._base_client.request(
            method="POST",
            path="/v2/PropertyDetailBulk",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2PropertyDetailBulkCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncPropertyDetailBulkClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        ids: typing.List[str],
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V2PropertyDetailBulkCreateResponse:
        """
        Property Detail Bulk API

        For retrieving up to 1000 properties at once. Can be used standalone, but it's designed
        to work together with the Property Search API. Use this API for quickly exporting lists,
        or bulk search requests for analytics.


        POST /v2/PropertyDetailBulk

        Args:
            ids: List of property IDs (max 1000)
            request_options: Additional options to customize the HTTP request

        Returns:
            Bulk property details retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v2.property_detail_bulk.create(ids=["string"])
        ```
        """
        _json = to_encodable(
            item={"ids": ids}, dump_with=params._SerializerPropertyBulkParameters
        )
        return await self._base_client.request(
            method="POST",
            path="/v2/PropertyDetailBulk",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V2PropertyDetailBulkCreateResponse,
            request_options=request_options or default_request_options(),
        )
