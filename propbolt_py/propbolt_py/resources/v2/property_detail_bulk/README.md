
### Property Detail Bulk API <a name="create"></a>

For retrieving up to 1000 properties at once. Can be used standalone, but it's designed 
to work together with the Property Search API. Use this API for quickly exporting lists, 
or bulk search requests for analytics.


**API Endpoint**: `POST /v2/PropertyDetailBulk`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v2.property_detail_bulk.create(ids=["string"])

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v2.property_detail_bulk.create(ids=["string"])

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `ids` | ✓ | List of property IDs (max 1000) | `["string"]` |
