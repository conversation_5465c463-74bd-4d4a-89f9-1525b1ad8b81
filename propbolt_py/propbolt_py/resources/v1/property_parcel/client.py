import typing

from propbolt_py.core import (
    AsyncBaseClient,
    RequestOptions,
    SyncBaseClient,
    default_request_options,
    to_encodable,
    type_utils,
)
from propbolt_py.types import models, params


class PropertyParcelClient:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client

    def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        apn: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        city: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        county: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        fips: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        house: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        latitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        longitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        polygon: typing.Union[
            typing.Optional[typing.List[params.GeoCoordinate]], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        radius: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        result_index: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        size: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        state: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        street: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        unit: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        zip: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V1PropertyParcelCreateResponse:
        """
        Property Boundary API

        Shape files API and property search API. All requests return the parcel boundaries
        in GEOJSON format. Quickly implement this API into your mapping applications.


        POST /v1/PropertyParcel

        Args:
            address: Fully formatted address
            apn: Assessor's Parcel Number
            city: City name
            county: County name
            fips: FIPS county code
            house: House number
            id: Property ID
            latitude: float
            longitude: float
            polygon: typing.List[GeoCoordinate]
            radius: float
            resultIndex: int
            size: int
            state: str
            street: Street name
            unit: Unit number
            zip: str
            request_options: Additional options to customize the HTTP request

        Returns:
            Property parcel boundaries retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        client.v1.property_parcel.create()
        ```
        """
        _json = to_encodable(
            item={
                "address": address,
                "apn": apn,
                "city": city,
                "county": county,
                "fips": fips,
                "house": house,
                "id": id,
                "latitude": latitude,
                "longitude": longitude,
                "polygon": polygon,
                "radius": radius,
                "result_index": result_index,
                "size": size,
                "state": state,
                "street": street,
                "unit": unit,
                "zip": zip,
            },
            dump_with=params._SerializerPropertyParcelParameters,
        )
        return self._base_client.request(
            method="POST",
            path="/v1/PropertyParcel",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V1PropertyParcelCreateResponse,
            request_options=request_options or default_request_options(),
        )


class AsyncPropertyParcelClient:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client

    async def create(
        self,
        *,
        address: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        apn: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        city: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        county: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        fips: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        house: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        id: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        latitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        longitude: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        polygon: typing.Union[
            typing.Optional[typing.List[params.GeoCoordinate]], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        radius: typing.Union[
            typing.Optional[float], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        result_index: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        size: typing.Union[
            typing.Optional[int], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        state: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        street: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        unit: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        zip: typing.Union[
            typing.Optional[str], type_utils.NotGiven
        ] = type_utils.NOT_GIVEN,
        request_options: typing.Optional[RequestOptions] = None,
    ) -> models.V1PropertyParcelCreateResponse:
        """
        Property Boundary API

        Shape files API and property search API. All requests return the parcel boundaries
        in GEOJSON format. Quickly implement this API into your mapping applications.


        POST /v1/PropertyParcel

        Args:
            address: Fully formatted address
            apn: Assessor's Parcel Number
            city: City name
            county: County name
            fips: FIPS county code
            house: House number
            id: Property ID
            latitude: float
            longitude: float
            polygon: typing.List[GeoCoordinate]
            radius: float
            resultIndex: int
            size: int
            state: str
            street: Street name
            unit: Unit number
            zip: str
            request_options: Additional options to customize the HTTP request

        Returns:
            Property parcel boundaries retrieved successfully

        Raises:
            ApiError: A custom exception class that provides additional context
                for API errors, including the HTTP status code and response body.

        Examples:
        ```py
        await client.v1.property_parcel.create()
        ```
        """
        _json = to_encodable(
            item={
                "address": address,
                "apn": apn,
                "city": city,
                "county": county,
                "fips": fips,
                "house": house,
                "id": id,
                "latitude": latitude,
                "longitude": longitude,
                "polygon": polygon,
                "radius": radius,
                "result_index": result_index,
                "size": size,
                "state": state,
                "street": street,
                "unit": unit,
                "zip": zip,
            },
            dump_with=params._SerializerPropertyParcelParameters,
        )
        return await self._base_client.request(
            method="POST",
            path="/v1/PropertyParcel",
            auth_names=["ApiKeyAuth"],
            json=_json,
            cast_to=models.V1PropertyParcelCreateResponse,
            request_options=request_options or default_request_options(),
        )
