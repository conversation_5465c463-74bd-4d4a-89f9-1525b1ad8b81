
### Property Boundary API <a name="create"></a>

Shape files API and property search API. All requests return the parcel boundaries 
in GEOJSON format. Quickly implement this API into your mapping applications.


**API Endpoint**: `POST /v1/PropertyParcel`

#### Synchronous Client

```python
from os import getenv
from propbolt_py import Client

client = Client(api_key=getenv("API_KEY"))
res = client.v1.property_parcel.create()

```

#### Asynchronous Client

```python
from os import getenv
from propbolt_py import AsyncClient

client = AsyncClient(api_key=getenv("API_KEY"))
res = await client.v1.property_parcel.create()

```

#### Parameters

| Parameter | Required | Description | Example |
|-----------|:--------:|-------------|--------|
| `address` | ✗ | Fully formatted address | `"string"` |
| `apn` | ✗ | Assessor's Parcel Number | `"string"` |
| `city` | ✗ | City name | `"string"` |
| `county` | ✗ | County name | `"string"` |
| `fips` | ✗ | FIPS county code | `"string"` |
| `house` | ✗ | House number | `"string"` |
| `id` | ✗ | Property ID | `"string"` |
| `latitude` | ✗ |  | `123.0` |
| `longitude` | ✗ |  | `123.0` |
| `polygon` | ✗ |  | `[{"lat": 123.0, "lon": 123.0}]` |
| `radius` | ✗ |  | `123.0` |
| `resultIndex` | ✗ |  | `123` |
| `size` | ✗ |  | `123` |
| `state` | ✗ |  | `"string"` |
| `street` | ✗ | Street name | `"string"` |
| `unit` | ✗ | Unit number | `"string"` |
| `zip` | ✗ |  | `"string"` |
