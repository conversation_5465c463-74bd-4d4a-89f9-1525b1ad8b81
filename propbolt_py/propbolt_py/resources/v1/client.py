from propbolt_py.core import AsyncBase<PERSON>lient, SyncBaseClient
from propbolt_py.resources.v1.property_parcel import (
    AsyncPropertyParcelClient,
    PropertyParcelClient,
)


class V1Client:
    def __init__(self, *, base_client: SyncBaseClient):
        self._base_client = base_client
        self.property_parcel = PropertyParcelClient(base_client=self._base_client)


class AsyncV1Client:
    def __init__(self, *, base_client: AsyncBaseClient):
        self._base_client = base_client
        self.property_parcel = AsyncPropertyParcelClient(base_client=self._base_client)
