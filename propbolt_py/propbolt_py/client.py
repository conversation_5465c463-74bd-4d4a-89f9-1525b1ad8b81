import httpx
import typing

from propbolt_py.core import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, S<PERSON><PERSON><PERSON><PERSON><PERSON>
from propbolt_py.environment import Environment, _get_base_url
from propbolt_py.resources.v1 import AsyncV1Client, V1Client
from propbolt_py.resources.v2 import AsyncV2<PERSON>lient, V2Client
from propbolt_py.resources.v3 import AsyncV3Client, V3Client


class Client:
    def __init__(
        self,
        *,
        timeout: typing.Optional[float] = 60,
        httpx_client: typing.Optional[httpx.Client] = None,
        base_url: typing.Optional[str] = None,
        environment: Environment = Environment.ENVIRONMENT,
        api_key: typing.Optional[str] = None,
    ):
        """Initialize root client"""
        self._base_client = SyncBaseClient(
            base_url=_get_base_url(base_url=base_url, environment=environment),
            httpx_client=httpx.Client(timeout=timeout)
            if httpx_client is None
            else httpx_client,
        )
        self._base_client.register_auth(
            "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(header_name="x-api-key", val=api_key)
        )
        self.v1 = V1Client(base_client=self._base_client)
        self.v2 = V2Client(base_client=self._base_client)
        self.v3 = V3Client(base_client=self._base_client)


class AsyncClient:
    def __init__(
        self,
        *,
        timeout: typing.Optional[float] = 60,
        httpx_client: typing.Optional[httpx.AsyncClient] = None,
        base_url: typing.Optional[str] = None,
        environment: Environment = Environment.ENVIRONMENT,
        api_key: typing.Optional[str] = None,
    ):
        """Initialize root client"""
        self._base_client = AsyncBaseClient(
            base_url=_get_base_url(base_url=base_url, environment=environment),
            httpx_client=httpx.AsyncClient(timeout=timeout)
            if httpx_client is None
            else httpx_client,
        )
        self._base_client.register_auth(
            "ApiKeyAuth", AuthKeyHeader(header_name="x-api-key", val=api_key)
        )
        self.v1 = AsyncV1Client(base_client=self._base_client)
        self.v2 = AsyncV2Client(base_client=self._base_client)
        self.v3 = AsyncV3Client(base_client=self._base_client)
