"""
Generated by <PERSON><PERSON> (sideko.dev)
"""

from json import JSONDecodeError
import typing
import httpx


class ApiError(Exception):
    """
    A custom exception class for handling API-related errors.

    This class extends the base Exception class to provide additional context
    for API errors, including the HTTP status code and response body.

    Attributes:
        status_code: The HTTP status code associated with the error.
            None if no status code is applicable.
        body: The response body or error message content.
            Can be any type depending on the API response format.
        response: The raw httpx response object. See https://www.python-httpx.org/api/#response for object reference
    """

    status_code: typing.Optional[int]
    body: typing.Any
    response: httpx.Response

    def __init__(self, *, response: httpx.Response) -> None:
        """
        Initialize the ApiError with optional status code and body.

        Args:
            status_code: The HTTP status code of the error.
                Defaults to None.

        Note:
            The asterisk (*) in the parameters forces keyword arguments,
            making the instantiation more explicit.
        """
        try:
            self.body = response.json()
        except JSONDecodeError:
            self.body = None
        self.status_code = response.status_code
        self.response = response

    def __str__(self) -> str:
        """
        Return a string representation of the ApiError.

        Returns:
            str: A formatted string containing the status code and body.
                Format: "status_code: {status_code}, body: {body}"
        """
        return f"status_code: {self.status_code}, body: {self.body}"
