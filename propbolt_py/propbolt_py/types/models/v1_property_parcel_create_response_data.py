import pydantic
import typing
import typing_extensions

from .v1_property_parcel_create_response_data_features_item import (
    V1PropertyParcelCreateResponseDataFeaturesItem,
)


class V1PropertyParcelCreateResponseData(pydantic.BaseModel):
    """
    GeoJSON FeatureCollection containing property boundaries
    """

    model_config = pydantic.ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
    )

    features: typing.Optional[
        typing.List[V1PropertyParcelCreateResponseDataFeaturesItem]
    ] = pydantic.Field(alias="features", default=None)
    type_: typing.Optional[typing_extensions.Literal["FeatureCollection"]] = (
        pydantic.Field(alias="type", default=None)
    )
