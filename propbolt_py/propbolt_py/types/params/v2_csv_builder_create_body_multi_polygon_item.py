import pydantic
import typing
import typing_extensions

from .geo_coordinate import GeoCoordinate, _SerializerGeoCoordinate


class V2CsvBuilderCreateBodyMultiPolygonItem(typing_extensions.TypedDict):
    """
    V2CsvBuilderCreateBodyMultiPolygonItem
    """

    boundaries: typing_extensions.NotRequired[typing.List[GeoCoordinate]]


class _SerializerV2CsvBuilderCreateBodyMultiPolygonItem(pydantic.BaseModel):
    """
    Serializer for V2CsvBuilderCreateBodyMultiPolygonItem handling case conversions
    and file omissions as dictated by the API
    """

    model_config = pydantic.ConfigDict(
        populate_by_name=True,
    )

    boundaries: typing.Optional[typing.List[_SerializerGeoCoordinate]] = pydantic.Field(
        alias="boundaries", default=None
    )
