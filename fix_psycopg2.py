#!/usr/bin/env python3
"""
Quick Fix for psycopg2 Installation Issue
Specifically addresses the PostgreSQL adapter dependency
"""

import os
import sys
import subprocess
import platform

def run_command(command):
    """Run a command and return success status"""
    print(f"🔧 Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def fix_psycopg2():
    """Fix psycopg2 installation"""
    print("🔧 Fixing psycopg2 installation...")
    
    system = platform.system()
    
    if system == "Darwin":  # macOS
        print("🍎 macOS detected - installing PostgreSQL dependencies...")
        
        # Check if Homebrew is available
        if run_command("which brew"):
            print("✅ Homebrew found")
            
            # Install PostgreSQL
            print("📦 Installing PostgreSQL...")
            run_command("brew install postgresql")
            
            # Install psycopg2-binary
            print("🐍 Installing psycopg2-binary...")
            if run_command("pip install psycopg2-binary"):
                print("✅ psycopg2-binary installed successfully")
                return True
            else:
                print("⚠️  pip install failed, trying with sudo...")
                return run_command("sudo pip install psycopg2-binary")
        else:
            print("❌ Homebrew not found. Please install Homebrew first:")
            print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
            return False
            
    elif system == "Linux":  # Linux
        print("🐧 Linux detected - installing PostgreSQL dependencies...")
        
        # Install system dependencies
        if os.path.exists("/usr/bin/apt"):  # Ubuntu/Debian
            print("📦 Installing PostgreSQL client libraries (Ubuntu/Debian)...")
            run_command("sudo apt update")
            run_command("sudo apt install -y postgresql-client libpq-dev python3-dev")
        elif os.path.exists("/usr/bin/yum"):  # CentOS/RHEL
            print("📦 Installing PostgreSQL client libraries (CentOS/RHEL)...")
            run_command("sudo yum install -y postgresql-devel python3-devel")
        elif os.path.exists("/usr/bin/dnf"):  # Fedora
            print("📦 Installing PostgreSQL client libraries (Fedora)...")
            run_command("sudo dnf install -y postgresql-devel python3-devel")
        
        # Install psycopg2-binary
        print("🐍 Installing psycopg2-binary...")
        return run_command("sudo pip install psycopg2-binary")
        
    else:
        print(f"❌ Unsupported system: {system}")
        print("Please install PostgreSQL client libraries manually")
        return False

def test_psycopg2():
    """Test if psycopg2 is working"""
    print("🧪 Testing psycopg2 installation...")
    
    try:
        import psycopg2
        print("✅ psycopg2 imported successfully")
        
        # Test connection (this will fail but should not crash)
        try:
            conn = psycopg2.connect(
                host="*************",
                port="5432",
                database="propbolt",
                user="propbolt_user",
                password="PropBolt2024!"
            )
            print("✅ Database connection test successful")
            conn.close()
            return True
        except psycopg2.Error as e:
            print(f"⚠️  Database connection failed (expected): {e}")
            print("✅ But psycopg2 is working correctly")
            return True
            
    except ImportError as e:
        print(f"❌ psycopg2 import failed: {e}")
        return False

def main():
    """Main fix function"""
    print("🔧 PropBolt API - psycopg2 Quick Fix")
    print("=" * 40)
    print("This script will fix the psycopg2 installation issue")
    print()
    
    # Check current status
    try:
        import psycopg2
        print("✅ psycopg2 is already installed")
        if test_psycopg2():
            print("\n🎉 psycopg2 is working correctly!")
            print("You can now run: sudo python pre_deploy_test.py")
            return
    except ImportError:
        print("❌ psycopg2 not found - proceeding with installation")
    
    print()
    
    # Fix the installation
    if fix_psycopg2():
        print("\n🧪 Testing installation...")
        if test_psycopg2():
            print("\n🎉 psycopg2 fixed successfully!")
            print("\n✅ Ready to run tests:")
            print("   sudo python pre_deploy_test.py")
        else:
            print("\n❌ psycopg2 installation verification failed")
            sys.exit(1)
    else:
        print("\n❌ Failed to install psycopg2")
        print("\n🔧 Manual installation options:")
        print("1. Install PostgreSQL: brew install postgresql (macOS)")
        print("2. Install psycopg2: pip install psycopg2-binary")
        print("3. Or use the full installer: sudo python install_deps.py")
        sys.exit(1)

if __name__ == "__main__":
    main()
