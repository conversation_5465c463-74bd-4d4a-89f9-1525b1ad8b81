#!/usr/bin/env python3
"""
Dependency Installation Script for PropBolt API
Handles PostgreSQL dependencies and other requirements
"""

import os
import sys
import subprocess
import platform

def run_command(command, check=True):
    """Run a command and return the result"""
    print(f"🔧 Running: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        if check:
            raise
        return e

def check_system():
    """Check system requirements"""
    print("🔍 Checking system...")
    print(f"OS: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    
    # Check if we're on macOS and need Homebrew
    if platform.system() == "Darwin":
        result = run_command("which brew", check=False)
        if result.returncode != 0:
            print("⚠️  Homebrew not found. Installing Homebrew...")
            run_command('/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"')
        else:
            print("✅ Homebrew available")

def install_postgresql_deps():
    """Install PostgreSQL dependencies"""
    print("\n📦 Installing PostgreSQL dependencies...")
    
    system = platform.system()
    
    if system == "Darwin":  # macOS
        print("🍎 Installing PostgreSQL dependencies for macOS...")
        
        # Install PostgreSQL via Homebrew
        run_command("sudo brew install postgresql", check=False)
        
        # Install Python PostgreSQL adapter
        run_command("sudo pip install psycopg2-binary")
        
    elif system == "Linux":  # Linux
        print("🐧 Installing PostgreSQL dependencies for Linux...")
        
        # Try different package managers
        if os.path.exists("/usr/bin/apt"):  # Ubuntu/Debian
            run_command("sudo apt update")
            run_command("sudo apt install -y postgresql-client libpq-dev python3-dev")
        elif os.path.exists("/usr/bin/yum"):  # CentOS/RHEL
            run_command("sudo yum install -y postgresql-devel python3-devel")
        elif os.path.exists("/usr/bin/dnf"):  # Fedora
            run_command("sudo dnf install -y postgresql-devel python3-devel")
        
        # Install Python PostgreSQL adapter
        run_command("sudo pip install psycopg2-binary")
        
    else:
        print(f"⚠️  Unsupported system: {system}")
        print("Please install PostgreSQL client libraries manually")

def install_python_deps():
    """Install Python dependencies"""
    print("\n🐍 Installing Python dependencies...")
    
    # Upgrade pip first
    run_command("sudo pip install --upgrade pip")
    
    # Install requirements
    run_command("sudo pip install -r requirements.txt")

def verify_installation():
    """Verify that all dependencies are installed correctly"""
    print("\n✅ Verifying installation...")
    
    try:
        import psycopg2
        print("✅ psycopg2 installed successfully")
    except ImportError:
        print("❌ psycopg2 installation failed")
        return False
    
    try:
        import asyncpg
        print("✅ asyncpg installed successfully")
    except ImportError:
        print("❌ asyncpg installation failed")
        return False
    
    try:
        import sqlalchemy
        print("✅ sqlalchemy installed successfully")
    except ImportError:
        print("❌ sqlalchemy installation failed")
        return False
    
    try:
        import fastapi
        print("✅ fastapi installed successfully")
    except ImportError:
        print("❌ fastapi installation failed")
        return False
    
    return True

def test_database_connection():
    """Test database connection"""
    print("\n🔍 Testing database connection...")
    
    try:
        # Set environment variables
        os.environ["DB_HOST"] = "*************"
        os.environ["DB_PORT"] = "5432"
        os.environ["DB_NAME"] = "propbolt"
        os.environ["DB_USER"] = "propbolt_user"
        os.environ["DB_PASSWORD"] = "PropBolt2024!"
        os.environ["DB_SSL_MODE"] = "prefer"
        
        from database.connection import DatabaseManager
        db_manager = DatabaseManager()
        
        if db_manager.test_sync_connection():
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
    except Exception as e:
        print(f"❌ Database connection test failed: {e}")
        return False

def main():
    """Main installation function"""
    print("📦 PropBolt API Dependency Installation")
    print("=" * 50)
    print("This script will install all required dependencies")
    print("including PostgreSQL client libraries")
    print()
    
    # Check if running with appropriate permissions
    if os.geteuid() != 0:
        print("⚠️  This script requires sudo privileges for system packages")
        print("Some installations may fail without sudo")
        print()
    
    try:
        # Run installation steps
        check_system()
        install_postgresql_deps()
        install_python_deps()
        
        # Verify installation
        if verify_installation():
            print("\n🎉 All dependencies installed successfully!")
            
            # Test database connection
            if test_database_connection():
                print("\n✅ Ready to run tests and deploy!")
                print("\nNext steps:")
                print("1. sudo python pre_deploy_test.py")
                print("2. sudo gcloud app deploy app.yaml")
            else:
                print("\n⚠️  Database connection failed")
                print("Please check your database configuration")
        else:
            print("\n❌ Some dependencies failed to install")
            print("Please check the error messages above")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n⏹️  Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
