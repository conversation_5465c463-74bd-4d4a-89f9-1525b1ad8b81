#!/usr/bin/env python3
"""
Test script for Property Details API using propbolt mock server
"""

import json
from propbolt_py import Client, AsyncClient
from propbolt_py.environment import Environment

def test_property_details_sync():
    """Test property details with synchronous client"""
    print("🏠 Testing Property Details API (Sync)")
    print("=" * 50)
    
    # Create client with mock server (no API key needed for mock)
    client = Client(api_key="test-key", environment=Environment.MOCK_SERVER)
    
    # Test cases
    test_addresses = [
        "123 Main St, Arlington, VA 22205",
        "456 Oak Ave, Herndon, VA 20170",
        "789 Pine Rd, Fairfax, VA 22030"
    ]
    
    for i, address in enumerate(test_addresses, 1):
        print(f"\n📍 Test {i}: {address}")
        print("-" * 40)
        
        try:
            # Call property details endpoint
            response = client.v2.property_detail.create(
                address=address,
                comps=True,  # Include comparables
                exact_match=False  # Allow fuzzy matching
            )
            
            print(f"✅ Success! Response type: {type(response)}")
            print(f"📄 Response preview:")
            
            # Pretty print the response (first 500 chars)
            response_str = str(response)
            if len(response_str) > 500:
                print(f"{response_str[:500]}...")
            else:
                print(response_str)
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)

async def test_property_details_async():
    """Test property details with asynchronous client"""
    print("\n🏠 Testing Property Details API (Async)")
    print("=" * 50)
    
    # Create async client with mock server
    client = AsyncClient(api_key="test-key", environment=Environment.MOCK_SERVER)
    
    try:
        print(f"\n📍 Async Test: 123 Main St, Arlington, VA 22205")
        print("-" * 40)
        
        # Call property details endpoint asynchronously
        response = await client.v2.property_detail.create(
            address="123 Main St, Arlington, VA 22205",
            comps=True,
            exact_match=False
        )
        
        print(f"✅ Async Success! Response type: {type(response)}")
        print(f"📄 Async Response preview:")
        
        # Pretty print the response
        response_str = str(response)
        if len(response_str) > 500:
            print(f"{response_str[:500]}...")
        else:
            print(response_str)
            
    except Exception as e:
        print(f"❌ Async Error: {e}")
    
    print("\n" + "=" * 50)

def test_property_details_variations():
    """Test different parameter combinations"""
    print("\n🔧 Testing Property Details API Variations")
    print("=" * 50)
    
    client = Client(api_key="test-key", environment=Environment.MOCK_SERVER)
    
    # Test different parameter combinations
    test_cases = [
        {
            "name": "Address only",
            "params": {"address": "123 Main St, Arlington, VA 22205"}
        },
        {
            "name": "Address with exact match",
            "params": {"address": "123 Main St, Arlington, VA 22205", "exact_match": True}
        },
        {
            "name": "City and State",
            "params": {"city": "Arlington", "state": "VA"}
        },
        {
            "name": "ZIP code search",
            "params": {"zip": "22205"}
        },
        {
            "name": "Street and City",
            "params": {"street": "Main St", "city": "Arlington", "state": "VA"}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['name']}")
        print(f"Parameters: {test_case['params']}")
        print("-" * 40)
        
        try:
            response = client.v2.property_detail.create(**test_case['params'])
            print(f"✅ Success! Response type: {type(response)}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)

def main():
    """Main test function"""
    print("🚀 Starting Property Details API Tests")
    print("🌐 Using Mock Server:", Environment.MOCK_SERVER.value)
    print("📋 Testing propbolt v0.1.0 API")
    
    # Run synchronous tests
    test_property_details_sync()
    
    # Run parameter variation tests
    test_property_details_variations()
    
    # Run async test
    import asyncio
    asyncio.run(test_property_details_async())
    
    print("\n🎉 All tests completed!")
    print("💡 Tip: Check the Sideko dashboard for request logs")

if __name__ == "__main__":
    main()
