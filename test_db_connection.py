#!/usr/bin/env python3
"""
Simple Database Connection Test
Tests connection to Google Cloud Production PostgreSQL
"""

import os
import sys

def test_database_connection():
    """Test database connection with proper SQLAlchemy 2.0 syntax"""
    print("🔍 Testing Google Cloud PostgreSQL Connection")
    print("=" * 50)
    
    # Set environment variables for Google Cloud Production PostgreSQL
    os.environ["DB_HOST"] = "*************"
    os.environ["DB_PORT"] = "5432"
    os.environ["DB_NAME"] = "propbolt"
    os.environ["DB_USER"] = "propbolt_user"
    os.environ["DB_PASSWORD"] = "PropBolt2024!"
    os.environ["DB_SSL_MODE"] = "prefer"
    os.environ["CLOUD_SQL_CONNECTION_NAME"] = "gold-braid-458901-v2:us-central1:propbolt-postgres"
    
    print(f"Host: {os.environ['DB_HOST']}:{os.environ['DB_PORT']}")
    print(f"Database: {os.environ['DB_NAME']}")
    print(f"User: {os.environ['DB_USER']}")
    print(f"SSL Mode: {os.environ['DB_SSL_MODE']}")
    print()
    
    try:
        print("📦 Importing database modules...")
        from database.connection import DatabaseManager
        print("✅ Database modules imported successfully")
        
        print("\n🔧 Creating database manager...")
        db_manager = DatabaseManager()
        print("✅ Database manager created")
        
        print("\n🧪 Testing synchronous connection...")
        if db_manager.test_sync_connection():
            print("✅ Synchronous database connection successful!")
        else:
            print("❌ Synchronous database connection failed")
            return False
        
        print("\n🧪 Testing asynchronous connection...")
        import asyncio
        async def test_async():
            return await db_manager.test_async_connection()
        
        if asyncio.run(test_async()):
            print("✅ Asynchronous database connection successful!")
        else:
            print("❌ Asynchronous database connection failed")
            return False
        
        print("\n🎉 All database connections working!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Run: sudo pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False

def main():
    """Main test function"""
    if test_database_connection():
        print("\n✅ Database connection test PASSED")
        print("\n🚀 Ready to run full tests:")
        print("   sudo python pre_deploy_test.py")
        print("   sudo python test_endpoints.py")
    else:
        print("\n❌ Database connection test FAILED")
        print("\n🔧 Troubleshooting:")
        print("1. Check if PostgreSQL dependencies are installed:")
        print("   sudo python fix_psycopg2.py")
        print("2. Verify database credentials")
        print("3. Check network connectivity to Google Cloud")
        sys.exit(1)

if __name__ == "__main__":
    main()
