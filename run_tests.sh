#!/bin/bash
# PropBolt API Test Runner
# Runs comprehensive endpoint tests with sudo privileges

set -e  # Exit on any error

echo "🧪 PropBolt API Test Runner"
echo "=========================="
echo "This script will test all endpoints before deployment"
echo ""

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
    echo "❌ This script requires sudo privileges"
    echo "Run: sudo ./run_tests.sh"
    exit 1
fi

echo "✅ Running with sudo privileges"
echo ""

# Install dependencies if needed
echo "📦 Installing dependencies..."
echo "This may take a few minutes for PostgreSQL dependencies..."
python install_deps.py

echo ""
echo "🔍 Running pre-deployment tests..."
echo "=================================="
python pre_deploy_test.py

echo ""
echo "🧪 Running comprehensive endpoint tests..."
echo "=========================================="
python test_endpoints.py

echo ""
echo "✅ All tests completed!"
echo ""
echo "🚀 Ready for deployment:"
echo "   sudo gcloud app deploy app.yaml"
echo ""
echo "📋 Post-deployment verification:"
echo "   curl https://data.propbolt.com/health"
