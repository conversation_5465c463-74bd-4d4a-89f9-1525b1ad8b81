#!/usr/bin/env python3
"""
Deployment Script for PropBolt API with PostgreSQL API Key Management
Handles database setup, migration, and Google Cloud deployment
"""

import os
import sys
import subprocess
import json
from typing import Dict, Any, Optional

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result"""
    print(f"🔧 Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if check and result.returncode != 0:
        print(f"❌ Command failed: {command}")
        print(f"Error: {result.stderr}")
        sys.exit(1)
    
    return result


def check_prerequisites():
    """Check if required tools are installed"""
    print("🔍 Checking prerequisites...")
    
    # Check Python
    try:
        import sqlalchemy
        import asyncpg
        import fastapi
        print("✅ Python dependencies available")
    except ImportError as e:
        print(f"❌ Missing Python dependency: {e}")
        print("Run: pip install -r requirements.txt")
        sys.exit(1)
    
    # Check Google Cloud CLI
    result = run_command("gcloud --version", check=False)
    if result.returncode != 0:
        print("❌ Google Cloud CLI not found")
        print("Install from: https://cloud.google.com/sdk/docs/install")
        sys.exit(1)
    print("✅ Google Cloud CLI available")
    
    # Check authentication
    result = run_command("gcloud auth list --filter=status:ACTIVE --format='value(account)'", check=False)
    if not result.stdout.strip():
        print("❌ Not authenticated with Google Cloud")
        print("Run: gcloud auth login")
        sys.exit(1)
    print(f"✅ Authenticated as: {result.stdout.strip()}")


def setup_database():
    """Set up the database schema"""
    print("\n📊 Setting up database...")
    
    # Check if database migration script exists
    if not os.path.exists("database/migrations.py"):
        print("❌ Database migration script not found")
        sys.exit(1)
    
    # Run database migration
    print("🔧 Running database migration...")
    result = run_command("python database/migrations.py", check=False)
    
    if result.returncode != 0:
        print("⚠️  Database migration had issues. Check the output above.")
        print("You may need to:")
        print("1. Update database credentials in app.yaml")
        print("2. Ensure database is accessible")
        print("3. Check firewall rules")
        
        continue_anyway = input("\nContinue with deployment anyway? (y/N): ").strip().lower()
        if continue_anyway not in ['y', 'yes']:
            sys.exit(1)
    else:
        print("✅ Database migration completed")


def update_configuration():
    """Update configuration files"""
    print("\n⚙️  Updating configuration...")
    
    # Check if app.yaml exists
    if not os.path.exists("app.yaml"):
        print("❌ app.yaml not found")
        sys.exit(1)
    
    # Read current app.yaml
    with open("app.yaml", "r") as f:
        content = f.read()
    
    # Check if database configuration is present
    if "DB_HOST" not in content:
        print("⚠️  Database configuration not found in app.yaml")
        print("Please update app.yaml with database environment variables")
        print("See API_KEY_MANAGEMENT.md for details")
        
        continue_anyway = input("\nContinue anyway? (y/N): ").strip().lower()
        if continue_anyway not in ['y', 'yes']:
            sys.exit(1)
    else:
        print("✅ Database configuration found in app.yaml")
    
    # Check for placeholder password
    if "your_db_password_here" in content:
        print("⚠️  Placeholder database password detected in app.yaml")
        print("Please update DB_PASSWORD with your actual database password")
        
        continue_anyway = input("\nContinue anyway? (y/N): ").strip().lower()
        if continue_anyway not in ['y', 'yes']:
            sys.exit(1)


def deploy_main_api():
    """Deploy the main API service"""
    print("\n🚀 Deploying main API service...")
    
    # Deploy to App Engine
    result = run_command("gcloud app deploy app.yaml --quiet", check=False)
    
    if result.returncode != 0:
        print("❌ Main API deployment failed")
        print("Check the error output above")
        return False
    
    print("✅ Main API deployed successfully")
    return True


def deploy_admin_interface():
    """Deploy the admin interface (optional)"""
    print("\n🔧 Deploying admin interface...")
    
    # Check if admin interface should be deployed
    deploy_admin = input("Deploy admin interface? (y/N): ").strip().lower()
    
    if deploy_admin not in ['y', 'yes']:
        print("⏭️  Skipping admin interface deployment")
        return True
    
    # Create admin.yaml if it doesn't exist
    if not os.path.exists("admin.yaml"):
        print("📝 Creating admin.yaml...")
        admin_config = """runtime: python39
service: propbolt-admin

env_variables:
  # Database Configuration
  DB_HOST: "*************"
  DB_PORT: "5432"
  DB_NAME: "propbolt"
  DB_USER: "propbolt_user"
  DB_PASSWORD: "PropBolt2024!"
  DB_SSL_MODE: "require"
  DATABASE_URL: "***********************************************************/propbolt?sslmode=require"

  # Admin API Key
  ADMIN_API_KEY: "admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  ADMIN_PORT: "8080"

automatic_scaling:
  min_instances: 0
  max_instances: 2
  target_cpu_utilization: 0.6

handlers:
- url: /.*
  script: auto

entrypoint: uvicorn admin_api:admin_app --host 0.0.0.0 --port $PORT
"""
        with open("admin.yaml", "w") as f:
            f.write(admin_config)
        print("✅ admin.yaml created")
    
    # Deploy admin service
    result = run_command("gcloud app deploy admin.yaml --quiet", check=False)
    
    if result.returncode != 0:
        print("❌ Admin interface deployment failed")
        print("Check the error output above")
        return False
    
    print("✅ Admin interface deployed successfully")
    return True


def test_deployment():
    """Test the deployed services"""
    print("\n🧪 Testing deployment...")
    
    # Get the deployed URL
    result = run_command("gcloud app browse --no-launch-browser", check=False)
    if result.returncode == 0:
        app_url = result.stdout.strip()
        print(f"📍 Main API URL: {app_url}")
        
        # Test health endpoint
        try:
            import requests
            response = requests.get(f"{app_url}/health", timeout=30)
            if response.status_code == 200:
                health_data = response.json()
                print("✅ Health check passed")
                print(f"   API Key Management: {health_data.get('api_key_management', 'unknown')}")
                print(f"   Database Status: {health_data.get('database', {}).get('overall', 'unknown')}")
            else:
                print(f"⚠️  Health check returned status {response.status_code}")
        except Exception as e:
            print(f"⚠️  Could not test health endpoint: {e}")
    
    print("\n📋 Deployment Summary:")
    print("=" * 50)
    print("✅ Main API deployed")
    print("✅ Database schema created")
    print("📖 Documentation: /docs")
    print("🔍 Health check: /health")
    print("🔧 Admin interface: /admin/docs (if deployed)")
    print("\n🔑 API Keys:")
    print("   Fallback AYO Key: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    print("   Admin Key: admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914")
    print("\n📚 Next Steps:")
    print("1. Create database-managed API keys via admin interface")
    print("2. Test API endpoints with new keys")
    print("3. Monitor usage and performance")
    print("4. Update customer integrations gradually")


def main():
    """Main deployment function"""
    print("🚀 PropBolt API Deployment Script")
    print("=" * 50)
    print("This script will:")
    print("1. Check prerequisites")
    print("2. Set up database schema")
    print("3. Deploy to Google Cloud")
    print("4. Test the deployment")
    print()
    
    # Confirm deployment
    confirm = input("Continue with deployment? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("Deployment cancelled")
        sys.exit(0)
    
    try:
        # Run deployment steps
        check_prerequisites()
        setup_database()
        update_configuration()
        
        if deploy_main_api():
            deploy_admin_interface()
            test_deployment()
            print("\n🎉 Deployment completed successfully!")
        else:
            print("\n❌ Deployment failed")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Deployment cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
