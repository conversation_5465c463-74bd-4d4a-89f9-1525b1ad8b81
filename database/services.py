#!/usr/bin/env python3
"""
API Key Management Services
Handles API key generation, validation, and usage tracking
"""

import hashlib
import json
from datetime import datetime, date, timedelta
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy import and_, func, desc
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from .models import APIKey, APIKeyStatus, APIKeyTier, APIUsageLog, DailyUsageSummary, APIKeyEvent
from .connection import get_sync_db_session, get_async_db_session, db_manager
import logging

logger = logging.getLogger(__name__)


class APIKeyService:
    """Service for managing API keys"""
    
    @staticmethod
    def create_api_key(
        name: str,
        description: Optional[str] = None,
        tier: APIKeyTier = APIKeyTier.FREE,
        user_id: Optional[str] = None,
        organization: Optional[str] = None,
        contact_email: Optional[str] = None,
        daily_quota: int = 1000,
        monthly_quota: int = 10000,
        rate_limit_per_minute: int = 60,
        expires_days: Optional[int] = None,
        is_test_key: bool = False
    ) -> Tuple[APIKey, str]:
        """Create a new API key and return the model and actual key"""
        
        # Generate the actual API key
        prefix = "pb_test_" if is_test_key else "pb_live_"
        actual_key = APIKey.generate_key(prefix)
        key_hash = APIKey.hash_key(actual_key)
        
        # Calculate expiration date
        expires_at = None
        if expires_days:
            expires_at = datetime.utcnow() + timedelta(days=expires_days)
        
        # Create the API key model
        api_key = APIKey(
            key_id=actual_key[:20],  # First 20 chars for identification
            key_hash=key_hash,
            key_prefix=prefix,
            name=name,
            description=description,
            tier=tier,
            user_id=user_id,
            organization=organization,
            contact_email=contact_email,
            daily_quota=daily_quota,
            monthly_quota=monthly_quota,
            rate_limit_per_minute=rate_limit_per_minute,
            expires_at=expires_at
        )
        
        # Save to database
        with db_manager.get_sync_session() as session:
            session.add(api_key)
            session.flush()  # Get the ID
            
            # Log the creation event
            event = APIKeyEvent(
                api_key_id=api_key.id,
                event_type="created",
                description=f"API key '{name}' created",
                metadata=json.dumps({
                    "tier": tier.value,
                    "daily_quota": daily_quota,
                    "monthly_quota": monthly_quota,
                    "is_test_key": is_test_key
                })
            )
            session.add(event)
            session.commit()
        
        logger.info(f"Created API key: {api_key.key_id} for {organization or user_id}")
        return api_key, actual_key
    
    @staticmethod
    def validate_api_key(api_key: str) -> Optional[APIKey]:
        """Validate an API key and return the model if valid"""
        if not api_key:
            return None
        
        # Hash the provided key
        key_hash = APIKey.hash_key(api_key)
        
        with db_manager.get_sync_session() as session:
            # Find the API key by hash
            api_key_model = session.query(APIKey).filter(
                APIKey.key_hash == key_hash
            ).first()
            
            if not api_key_model:
                return None
            
            # Check if the key is valid
            if not api_key_model.is_valid():
                return None
            
            # Update last used timestamp
            api_key_model.last_used_at = datetime.utcnow()
            session.commit()
            
            return api_key_model
    
    @staticmethod
    async def validate_api_key_async(api_key: str) -> Optional[APIKey]:
        """Async version of API key validation"""
        if not api_key:
            return None
        
        key_hash = APIKey.hash_key(api_key)
        
        async with db_manager.get_async_session() as session:
            # Find the API key by hash
            result = await session.execute(
                session.query(APIKey).filter(APIKey.key_hash == key_hash)
            )
            api_key_model = result.scalar_one_or_none()
            
            if not api_key_model:
                return None
            
            if not api_key_model.is_valid():
                return None
            
            # Update last used timestamp
            api_key_model.last_used_at = datetime.utcnow()
            await session.commit()
            
            return api_key_model
    
    @staticmethod
    def get_api_key_by_id(key_id: str) -> Optional[APIKey]:
        """Get API key by its ID"""
        with db_manager.get_sync_session() as session:
            return session.query(APIKey).filter(APIKey.key_id == key_id).first()
    
    @staticmethod
    def update_api_key_status(key_id: str, status: APIKeyStatus, reason: Optional[str] = None) -> bool:
        """Update API key status"""
        with db_manager.get_sync_session() as session:
            api_key = session.query(APIKey).filter(APIKey.key_id == key_id).first()
            if not api_key:
                return False
            
            old_status = api_key.status
            api_key.status = status
            api_key.updated_at = datetime.utcnow()
            
            # Log the status change
            event = APIKeyEvent(
                api_key_id=api_key.id,
                event_type="status_changed",
                description=f"Status changed from {old_status.value} to {status.value}",
                metadata=json.dumps({"reason": reason, "old_status": old_status.value, "new_status": status.value})
            )
            session.add(event)
            session.commit()
            
            logger.info(f"Updated API key {key_id} status to {status.value}")
            return True
    
    @staticmethod
    def list_api_keys(
        user_id: Optional[str] = None,
        organization: Optional[str] = None,
        status: Optional[APIKeyStatus] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[APIKey]:
        """List API keys with optional filters"""
        with db_manager.get_sync_session() as session:
            query = session.query(APIKey)
            
            if user_id:
                query = query.filter(APIKey.user_id == user_id)
            if organization:
                query = query.filter(APIKey.organization == organization)
            if status:
                query = query.filter(APIKey.status == status)
            
            return query.order_by(desc(APIKey.created_at)).offset(offset).limit(limit).all()


class UsageTrackingService:
    """Service for tracking API usage"""
    
    @staticmethod
    def log_api_usage(
        api_key_id: str,
        endpoint: str,
        method: str,
        status_code: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None,
        response_time_ms: Optional[int] = None,
        error_message: Optional[str] = None
    ):
        """Log API usage"""
        usage_log = APIUsageLog(
            api_key_id=api_key_id,
            endpoint=endpoint,
            method=method,
            status_code=status_code,
            ip_address=ip_address,
            user_agent=user_agent,
            request_size=request_size,
            response_size=response_size,
            response_time_ms=response_time_ms,
            error_message=error_message
        )
        
        with db_manager.get_sync_session() as session:
            session.add(usage_log)
            session.commit()
    
    @staticmethod
    async def log_api_usage_async(
        api_key_id: str,
        endpoint: str,
        method: str,
        status_code: int,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None,
        response_time_ms: Optional[int] = None,
        error_message: Optional[str] = None
    ):
        """Async version of usage logging"""
        usage_log = APIUsageLog(
            api_key_id=api_key_id,
            endpoint=endpoint,
            method=method,
            status_code=status_code,
            ip_address=ip_address,
            user_agent=user_agent,
            request_size=request_size,
            response_size=response_size,
            response_time_ms=response_time_ms,
            error_message=error_message
        )
        
        async with db_manager.get_async_session() as session:
            session.add(usage_log)
            await session.commit()
    
    @staticmethod
    def get_daily_usage(api_key_id: str, target_date: Optional[date] = None) -> int:
        """Get daily usage count for an API key"""
        if target_date is None:
            target_date = date.today()
        
        with db_manager.get_sync_session() as session:
            # First check the summary table
            summary = session.query(DailyUsageSummary).filter(
                and_(
                    DailyUsageSummary.api_key_id == api_key_id,
                    DailyUsageSummary.date == target_date
                )
            ).first()
            
            if summary:
                return summary.total_requests
            
            # If no summary, count from logs
            count = session.query(func.count(APIUsageLog.id)).filter(
                and_(
                    APIUsageLog.api_key_id == api_key_id,
                    func.date(APIUsageLog.timestamp) == target_date
                )
            ).scalar()
            
            return count or 0
    
    @staticmethod
    def check_quota_exceeded(api_key: APIKey) -> Dict[str, Any]:
        """Check if API key has exceeded quotas"""
        daily_usage = UsageTrackingService.get_daily_usage(api_key.id)
        
        # For monthly usage, we'd need to implement similar logic
        # For now, just check daily
        
        return {
            "daily_quota_exceeded": api_key.is_quota_exceeded(daily_usage, "daily"),
            "daily_usage": daily_usage,
            "daily_quota": api_key.daily_quota,
            "daily_remaining": max(0, api_key.daily_quota - daily_usage)
        }
    
    @staticmethod
    def update_daily_summary(api_key_id: str, target_date: Optional[date] = None):
        """Update daily usage summary for an API key"""
        if target_date is None:
            target_date = date.today()
        
        with db_manager.get_sync_session() as session:
            # Calculate statistics from logs
            stats = session.query(
                func.count(APIUsageLog.id).label('total_requests'),
                func.count(APIUsageLog.id).filter(APIUsageLog.status_code < 400).label('successful_requests'),
                func.count(APIUsageLog.id).filter(APIUsageLog.status_code >= 400).label('failed_requests'),
                func.avg(APIUsageLog.response_time_ms).label('avg_response_time'),
                func.sum(func.coalesce(APIUsageLog.request_size, 0) + func.coalesce(APIUsageLog.response_size, 0)).label('total_data')
            ).filter(
                and_(
                    APIUsageLog.api_key_id == api_key_id,
                    func.date(APIUsageLog.timestamp) == target_date
                )
            ).first()
            
            # Update or create summary
            summary = session.query(DailyUsageSummary).filter(
                and_(
                    DailyUsageSummary.api_key_id == api_key_id,
                    DailyUsageSummary.date == target_date
                )
            ).first()
            
            if summary:
                summary.total_requests = stats.total_requests or 0
                summary.successful_requests = stats.successful_requests or 0
                summary.failed_requests = stats.failed_requests or 0
                summary.avg_response_time_ms = int(stats.avg_response_time) if stats.avg_response_time else None
                summary.total_data_transferred = stats.total_data or 0
                summary.updated_at = datetime.utcnow()
            else:
                summary = DailyUsageSummary(
                    api_key_id=api_key_id,
                    date=target_date,
                    total_requests=stats.total_requests or 0,
                    successful_requests=stats.successful_requests or 0,
                    failed_requests=stats.failed_requests or 0,
                    avg_response_time_ms=int(stats.avg_response_time) if stats.avg_response_time else None,
                    total_data_transferred=stats.total_data or 0
                )
                session.add(summary)
            
            session.commit()
