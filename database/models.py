#!/usr/bin/env python3
"""
Database Models for PropBolt API Key Management
PostgreSQL-based API key management system for data.propbolt.com
"""

import enum
import uuid
import secrets
from datetime import datetime, timedelta
from typing import Optional, List
from sqlalchemy import (
    Column, String, Integer, DateTime, Boolean, Text, 
    ForeignKey, Index, BigInteger, Enum as SQLEnum
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import sqlalchemy as sa

Base = declarative_base()


class APIKeyStatus(enum.Enum):
    """API Key status enumeration"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    REVOKED = "revoked"
    EXPIRED = "expired"


class APIKeyTier(enum.Enum):
    """API Key tier/plan enumeration"""
    FREE = "free"
    BASIC = "basic"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    UNLIMITED = "unlimited"


class APIKey(Base):
    """API Key model for authentication and authorization"""
    __tablename__ = "api_keys"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Key identification
    key_id = Column(String(50), unique=True, nullable=False, index=True)
    key_hash = Column(String(128), nullable=False)  # SHA-256 hash of the actual key
    key_prefix = Column(String(20), nullable=False)  # pb_live_ or pb_test_
    
    # Key metadata
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Status and tier
    status = Column(SQLEnum(APIKeyStatus), default=APIKeyStatus.ACTIVE, nullable=False)
    tier = Column(SQLEnum(APIKeyTier), default=APIKeyTier.FREE, nullable=False)
    
    # Ownership
    user_id = Column(String(255), nullable=True)  # External user identifier
    organization = Column(String(255), nullable=True)
    contact_email = Column(String(255), nullable=True)
    
    # Quotas and limits
    daily_quota = Column(Integer, default=1000, nullable=False)
    monthly_quota = Column(Integer, default=10000, nullable=False)
    rate_limit_per_minute = Column(Integer, default=60, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=True)
    last_used_at = Column(DateTime, nullable=True)
    
    # IP restrictions (JSON array of allowed IPs/CIDR blocks)
    allowed_ips = Column(Text, nullable=True)  # JSON string
    
    # Relationships
    usage_logs = relationship("APIUsageLog", back_populates="api_key", cascade="all, delete-orphan")
    daily_summaries = relationship("DailyUsageSummary", back_populates="api_key", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index('idx_api_keys_status_tier', 'status', 'tier'),
        Index('idx_api_keys_user_org', 'user_id', 'organization'),
        Index('idx_api_keys_created_at', 'created_at'),
        Index('idx_api_keys_expires_at', 'expires_at'),
    )

    @classmethod
    def generate_key(cls, prefix: str = "pb_live_") -> str:
        """Generate a new API key with the specified prefix"""
        # Generate 32 random bytes and encode as hex
        random_part = secrets.token_hex(32)
        return f"{prefix}{random_part}"

    @classmethod
    def hash_key(cls, key: str) -> str:
        """Hash an API key for secure storage"""
        import hashlib
        return hashlib.sha256(key.encode()).hexdigest()

    def is_valid(self) -> bool:
        """Check if the API key is valid and active"""
        if self.status != APIKeyStatus.ACTIVE:
            return False
        
        if self.expires_at and self.expires_at < datetime.utcnow():
            return False
            
        return True

    def is_quota_exceeded(self, usage_count: int, period: str = "daily") -> bool:
        """Check if quota is exceeded for the given period"""
        if period == "daily":
            return usage_count >= self.daily_quota
        elif period == "monthly":
            return usage_count >= self.monthly_quota
        return False


class APIUsageLog(Base):
    """Detailed API usage logging for analytics and debugging"""
    __tablename__ = "api_usage_logs"

    # Primary key
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    
    # Foreign key to API key
    api_key_id = Column(UUID(as_uuid=True), ForeignKey("api_keys.id"), nullable=False)
    
    # Request details
    endpoint = Column(String(255), nullable=False)
    method = Column(String(10), nullable=False)
    status_code = Column(Integer, nullable=False)
    
    # Request metadata
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    request_size = Column(Integer, nullable=True)  # bytes
    response_size = Column(Integer, nullable=True)  # bytes
    response_time_ms = Column(Integer, nullable=True)  # milliseconds
    
    # Timestamps
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Error details (if any)
    error_message = Column(Text, nullable=True)
    
    # Relationships
    api_key = relationship("APIKey", back_populates="usage_logs")

    # Indexes for performance
    __table_args__ = (
        Index('idx_usage_logs_api_key_timestamp', 'api_key_id', 'timestamp'),
        Index('idx_usage_logs_endpoint', 'endpoint'),
        Index('idx_usage_logs_timestamp', 'timestamp'),
        Index('idx_usage_logs_status_code', 'status_code'),
    )


class DailyUsageSummary(Base):
    """Daily usage summaries for efficient quota checking"""
    __tablename__ = "daily_usage_summaries"

    # Composite primary key
    api_key_id = Column(UUID(as_uuid=True), ForeignKey("api_keys.id"), primary_key=True)
    date = Column(sa.Date, primary_key=True)
    
    # Usage statistics
    total_requests = Column(Integer, default=0, nullable=False)
    successful_requests = Column(Integer, default=0, nullable=False)
    failed_requests = Column(Integer, default=0, nullable=False)
    
    # Performance metrics
    avg_response_time_ms = Column(Integer, nullable=True)
    total_data_transferred = Column(BigInteger, default=0, nullable=False)  # bytes
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    api_key = relationship("APIKey", back_populates="daily_summaries")

    # Indexes
    __table_args__ = (
        Index('idx_daily_usage_date', 'date'),
        Index('idx_daily_usage_api_key_date', 'api_key_id', 'date'),
    )


class APIKeyEvent(Base):
    """Audit log for API key lifecycle events"""
    __tablename__ = "api_key_events"

    # Primary key
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    
    # Foreign key to API key
    api_key_id = Column(UUID(as_uuid=True), ForeignKey("api_keys.id"), nullable=False)
    
    # Event details
    event_type = Column(String(50), nullable=False)  # created, updated, suspended, revoked, etc.
    description = Column(Text, nullable=True)
    
    # Actor information
    actor_id = Column(String(255), nullable=True)  # Who performed the action
    actor_ip = Column(String(45), nullable=True)
    
    # Event metadata
    event_metadata = Column(Text, nullable=True)  # JSON string for additional data
    
    # Timestamp
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Indexes
    __table_args__ = (
        Index('idx_api_key_events_api_key_timestamp', 'api_key_id', 'timestamp'),
        Index('idx_api_key_events_event_type', 'event_type'),
        Index('idx_api_key_events_timestamp', 'timestamp'),
    )
