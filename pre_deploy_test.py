#!/usr/bin/env python3
"""
Pre-Deployment Test Suite for PropBolt API
Quick validation of critical endpoints before deploying to data.propbolt.com
"""

import os
import sys
import time
import json
import requests
import subprocess
from datetime import datetime

# Configuration
API_KEY = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
ADMIN_API_KEY = "admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
BASE_URL = "http://localhost:8080"

def run_sudo_command(command):
    """Run command with sudo"""
    try:
        result = subprocess.run(
            ["sudo"] + command.split() if isinstance(command, str) else ["sudo"] + command,
            capture_output=True,
            text=True,
            check=True
        )
        return result
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        return None

def check_dependencies():
    """Check if all dependencies are installed"""
    print("📦 Checking dependencies...")
    
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import asyncpg
        print("✅ All Python dependencies available")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Run: sudo pip install -r requirements.txt")
        return False

def test_database_connection():
    """Test database connection"""
    print("🔍 Testing database connection...")
    
    try:
        # Set environment variables
        os.environ["DB_HOST"] = "*************"
        os.environ["DB_PORT"] = "5432"
        os.environ["DB_NAME"] = "propbolt"
        os.environ["DB_USER"] = "propbolt_user"
        os.environ["DB_PASSWORD"] = "PropBolt2024!"
        os.environ["DB_SSL_MODE"] = "prefer"
        
        from database.connection import DatabaseManager
        db_manager = DatabaseManager()
        
        if db_manager.test_sync_connection():
            print("✅ Database connection successful")
            return True
        else:
            print("❌ Database connection failed")
            return False
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def start_server():
    """Start the API server"""
    print("🚀 Starting API server...")
    
    # Kill any existing processes
    run_sudo_command("pkill -f 'python main.py'")
    time.sleep(2)
    
    # Start server in background
    process = subprocess.Popen(
        ["sudo", "python", "main.py"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
    )
    
    print("⏳ Waiting for server startup...")
    time.sleep(8)  # Give server time to start
    
    return process

def test_health_endpoint():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint working")
            print(f"   API Key Management: {data.get('api_key_management', 'unknown')}")
            print(f"   Database Status: {data.get('database', {}).get('overall', 'unknown')}")
            return True
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_critical_endpoints():
    """Test the most critical API endpoints"""
    print("🧪 Testing critical API endpoints...")
    
    headers = {
        "x-api-key": API_KEY,
        "Content-Type": "application/json"
    }
    
    # Test cases for critical endpoints
    test_cases = [
        {
            "name": "PropertyDetail",
            "endpoint": "/v2/PropertyDetail",
            "payload": {"address": "123 Main St, Anytown, CA 90210"}
        },
        {
            "name": "PropertySearch", 
            "endpoint": "/v2/PropertySearch",
            "payload": {"address": "123 Main St, Anytown, CA 90210", "radius": 1.0}
        },
        {
            "name": "AutoComplete",
            "endpoint": "/v2/AutoComplete", 
            "payload": {"query": "123 Main St"}
        }
    ]
    
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        try:
            response = requests.post(
                f"{BASE_URL}{test_case['endpoint']}",
                json=test_case["payload"],
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ {test_case['name']} - Working")
                passed += 1
            elif response.status_code == 401:
                print(f"❌ {test_case['name']} - Authentication failed")
            else:
                print(f"⚠️  {test_case['name']} - Status: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {test_case['name']} - Error: {e}")
    
    print(f"\n📊 Critical endpoints: {passed}/{total} working")
    return passed >= (total * 0.67)  # At least 67% should work

def test_admin_interface():
    """Test admin interface"""
    print("🔧 Testing admin interface...")
    
    try:
        headers = {"x-admin-key": ADMIN_API_KEY}
        response = requests.get(f"{BASE_URL}/admin/health", headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ Admin interface working")
            return True
        else:
            print(f"❌ Admin interface failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Admin interface error: {e}")
        return False

def test_api_key_authentication():
    """Test API key authentication"""
    print("🔑 Testing API key authentication...")
    
    # Test with valid key
    headers = {"x-api-key": API_KEY, "Content-Type": "application/json"}
    try:
        response = requests.post(
            f"{BASE_URL}/v2/PropertyDetail",
            json={"address": "123 Main St"},
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 200:
            print("✅ Valid API key authentication working")
        else:
            print(f"⚠️  Valid API key returned: {response.status_code}")
    except Exception as e:
        print(f"❌ Valid API key test failed: {e}")
        return False
    
    # Test with invalid key
    headers = {"x-api-key": "invalid-key", "Content-Type": "application/json"}
    try:
        response = requests.post(
            f"{BASE_URL}/v2/PropertyDetail",
            json={"address": "123 Main St"},
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 401:
            print("✅ Invalid API key properly rejected")
            return True
        else:
            print(f"⚠️  Invalid API key returned: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Invalid API key test failed: {e}")
        return False

def stop_server(process):
    """Stop the server"""
    print("⏹️  Stopping server...")
    
    try:
        run_sudo_command("pkill -f 'python main.py'")
        if process:
            process.terminate()
            process.wait(timeout=5)
    except Exception as e:
        print(f"⚠️  Error stopping server: {e}")

def main():
    """Main pre-deployment test function"""
    print("🚀 PropBolt API Pre-Deployment Test Suite")
    print("=" * 50)
    print("Testing critical functionality before deploying to data.propbolt.com")
    print()
    
    # Check sudo access
    try:
        subprocess.run(["sudo", "-n", "true"], check=True, capture_output=True)
        print("✅ Sudo access confirmed")
    except subprocess.CalledProcessError:
        print("❌ This script requires sudo access")
        print("Run: sudo python pre_deploy_test.py")
        sys.exit(1)
    
    server_process = None
    all_tests_passed = True
    
    try:
        # Run pre-flight checks
        print("🔍 Pre-flight checks:")
        print("-" * 20)
        
        if not check_dependencies():
            all_tests_passed = False
        
        if not test_database_connection():
            all_tests_passed = False
        
        if not all_tests_passed:
            print("\n❌ Pre-flight checks failed. Fix issues before continuing.")
            sys.exit(1)
        
        # Start server and run tests
        print("\n🧪 Running API tests:")
        print("-" * 20)
        
        server_process = start_server()
        
        # Core functionality tests
        tests = [
            test_health_endpoint(),
            test_api_key_authentication(),
            test_critical_endpoints(),
            test_admin_interface()
        ]
        
        passed_tests = sum(tests)
        total_tests = len(tests)
        
        print("\n" + "=" * 50)
        print("📊 PRE-DEPLOYMENT TEST RESULTS")
        print("=" * 50)
        print(f"Tests Passed: {passed_tests}/{total_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests >= total_tests * 0.75:  # 75% pass rate
            print("\n🎉 READY FOR DEPLOYMENT!")
            print("✅ Critical functionality verified")
            print("\n🚀 Deploy to data.propbolt.com:")
            print("   sudo gcloud app deploy app.yaml")
            print("\n📋 Post-deployment verification:")
            print("   curl https://data.propbolt.com/health")
        else:
            print("\n⚠️  NOT READY FOR DEPLOYMENT")
            print("❌ Critical tests failed")
            print("Please fix the issues before deploying")
            all_tests_passed = False
        
    except KeyboardInterrupt:
        print("\n⏹️  Testing cancelled by user")
        all_tests_passed = False
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
        all_tests_passed = False
    finally:
        if server_process:
            stop_server(server_process)
    
    if not all_tests_passed:
        sys.exit(1)

if __name__ == "__main__":
    main()
