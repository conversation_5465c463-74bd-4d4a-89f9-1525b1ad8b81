# Deployment Guide for data.propbolt.com

## 🎯 Deployment Target
- **Domain**: data.propbolt.com
- **Platform**: Google Cloud App Engine
- **Project**: gold-braid-458901-v2
- **Region**: us-central1
- **Service**: data-propbolt-api

## 🚀 Quick Deployment

### Prerequisites
```bash
# Ensure you're authenticated with Google Cloud
gcloud auth login
gcloud config set project gold-braid-458901-v2

# Verify current project
gcloud config get-value project
```

### One-Command Setup & Deploy
```bash
# Run the complete setup and deployment
python setup.py && gcloud app deploy app.yaml
```

### Manual Deployment Steps
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Set up database schema
python database/migrations.py

# 3. Deploy to data.propbolt.com
gcloud app deploy app.yaml

# 4. Verify deployment
curl https://data.propbolt.com/health
```

## 🔧 Configuration Summary

### App Engine Configuration (app.yaml)
```yaml
runtime: python39
service: data-propbolt-api
env_variables:
  # PORT is automatically set by App Engine
  DB_HOST: "*************"
  DB_NAME: "propbolt"
  DB_USER: "propbolt_user"
  DB_PASSWORD: "PropBolt2024!"
  DB_SSL_MODE: "prefer"
  CLOUD_SQL_CONNECTION_NAME: "gold-braid-458901-v2:us-central1:propbolt-postgres"
  API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  ADMIN_API_KEY: "admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
```

### Database Configuration (from gcloud sql)
- **Instance**: `propbolt-postgres`
- **Connection**: `gold-braid-458901-v2:us-central1:propbolt-postgres`
- **Host**: *************:5432
- **Database**: propbolt
- **User**: propbolt_user
- **SSL**: Prefer (flexible encryption)
- **Connection**: PostgreSQL with connection pooling + Cloud SQL proxy

## 🌐 Access Points

### Main API
- **Base URL**: https://data.propbolt.com
- **Health Check**: https://data.propbolt.com/health
- **API Documentation**: https://data.propbolt.com/docs
- **OpenAPI Spec**: https://data.propbolt.com/openapi.json

### Admin Interface
- **Admin Panel**: https://data.propbolt.com/admin/docs
- **Admin Key**: `admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914`

### API Endpoints (13 Total)
```
POST /v2/PropertySearch
POST /v2/PropertyDetail
POST /v2/PropertyDetailBulk
POST /v1/PropertyParcel
POST /v2/PropertyComps
POST /v3/PropertyComps
POST /v2/AutoComplete
POST /v2/AddressVerification
POST /v2/PropGPT
POST /v2/CSVBuilder
POST /v2/PropertyAvm
POST /v2/Reports/PropertyLiens
POST /v2/PropertyMapping
```

## 🔑 API Key Management

### Existing Keys (Backward Compatible)
```bash
# AYO Key (continues to work)
curl -X POST "https://data.propbolt.com/v2/PropertyDetail" \
  -H "x-api-key: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914" \
  -H "Content-Type: application/json" \
  -d '{"address": "123 Main St, Anytown, CA 90210"}'
```

### New Database-Managed Keys
```bash
# Create new API key via admin interface
curl -X POST "https://data.propbolt.com/admin/api-keys" \
  -H "x-admin-key: admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Production API Key",
    "tier": "professional",
    "daily_quota": 50000,
    "monthly_quota": 1000000
  }'
```

## 📊 Monitoring & Health

### Health Check Response
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00Z",
  "service": "PropBolt API",
  "version": "1.0.0",
  "api_key_management": "database",
  "database": {
    "sync_connection": "healthy",
    "async_connection": "healthy",
    "overall": "healthy"
  }
}
```

### Usage Monitoring
```bash
# Check API key usage
curl "https://data.propbolt.com/admin/api-keys/{key_id}/usage" \
  -H "x-admin-key: admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
```

## 🔒 Security Features

### Authentication Layers
1. **Fallback AYO Key**: Backward compatibility
2. **Database Keys**: Full quota and rate limiting
3. **Admin Interface**: Separate admin authentication
4. **IP Logging**: Request source tracking

### Rate Limiting
- **Headers**: `X-RateLimit-Limit`, `X-RateLimit-Remaining`
- **Quotas**: Daily/monthly limits per key
- **Enforcement**: Database-driven quota checking

## 🚨 Troubleshooting

### Common Issues
```bash
# Check deployment status
gcloud app versions list --service=data-propbolt-api

# View logs
gcloud app logs tail --service=data-propbolt-api

# Test database connection
python -c "from database.connection import DatabaseManager; print('✅ Connected' if DatabaseManager().test_sync_connection() else '❌ Failed')"
```

### Rollback if Needed
```bash
# List previous versions
gcloud app versions list --service=data-propbolt-api

# Rollback to previous version
gcloud app versions migrate [PREVIOUS_VERSION] --service=data-propbolt-api
```

## 📈 Post-Deployment

### Immediate Testing
```bash
# Test health endpoint
curl https://data.propbolt.com/health

# Test with existing AYO key
curl -X POST "https://data.propbolt.com/v2/PropertyDetail" \
  -H "x-api-key: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914" \
  -H "Content-Type: application/json" \
  -d '{"address": "123 Main St, Anytown, CA 90210"}'

# Access admin interface
open https://data.propbolt.com/admin/docs
```

### Next Steps
1. **Create Database Keys**: Use admin interface to generate new API keys
2. **Monitor Usage**: Track API usage through admin panel
3. **Update Integrations**: Gradually migrate customers to new keys
4. **Performance Monitoring**: Monitor database and API performance

## 🎉 Success Indicators
- ✅ Health endpoint returns "healthy"
- ✅ Database connections are "healthy"
- ✅ API key management shows "database"
- ✅ All 13 endpoints respond correctly
- ✅ Admin interface is accessible
- ✅ Existing AYO key continues to work

Your data.propbolt.com API is now live with enterprise-grade API key management! 🚀
