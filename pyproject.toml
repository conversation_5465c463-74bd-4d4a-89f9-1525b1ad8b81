[tool.poetry]
name = "real_estate_api_py"
version = "0.3.0"
description = ""
readme = "README.md"
authors = []
packages = [{ include = "real_estate_api_py" }]

[tool.poetry.dependencies]
python = "^3.8"
httpx = ">=0.26.0, <1"
pydantic = "^2.5.0"
typing_extensions = "^4.0.0"
jsonpointer = "^3.0.0"
fastapi = "^0.104.0"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
asyncpg = "^0.29.0"
sqlalchemy = "^2.0.0"
psycopg2-binary = "^2.9.0"
greenlet = "^2.0.0"
alembic = "^1.13.0"
python-multipart = "^0.0.6"

[tool.poetry.dev-dependencies]
mypy = "^1.8.0"
pytest = "^7.4.0"
pytest-asyncio = "^0.23.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
