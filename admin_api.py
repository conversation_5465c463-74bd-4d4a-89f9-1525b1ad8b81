#!/usr/bin/env python3
"""
Admin API for PropBolt API Key Management
Provides endpoints for managing API keys, viewing usage, and administration
"""

import os
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from fastapi import FastAPI, HTTPException, Depends, Query, status
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from database.models import APIKeyStatus, APIKeyTier
from database.services import APIKeyService, UsageTrackingService
from database.connection import startup_database, shutdown_database, check_database_health
from database.middleware import database_key_dependency
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
admin_app = FastAPI(
    title="PropBolt API Key Management",
    description="Admin interface for managing API keys and monitoring usage",
    version="1.0.0",
    docs_url="/admin/docs",
    redoc_url="/admin/redoc"
)

# Add CORS middleware
admin_app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://data.propbolt.com", "https://propbolt.com", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for requests/responses
class CreateAPIKeyRequest(BaseModel):
    name: str = Field(..., description="Human-readable name for the API key")
    description: Optional[str] = Field(None, description="Optional description")
    tier: APIKeyTier = Field(APIKeyTier.FREE, description="API key tier/plan")
    user_id: Optional[str] = Field(None, description="User identifier")
    organization: Optional[str] = Field(None, description="Organization name")
    contact_email: Optional[str] = Field(None, description="Contact email")
    daily_quota: int = Field(1000, description="Daily request quota")
    monthly_quota: int = Field(10000, description="Monthly request quota")
    rate_limit_per_minute: int = Field(60, description="Rate limit per minute")
    expires_days: Optional[int] = Field(None, description="Expiration in days")
    is_test_key: bool = Field(False, description="Whether this is a test key")


class APIKeyResponse(BaseModel):
    id: str
    key_id: str
    name: str
    description: Optional[str]
    status: APIKeyStatus
    tier: APIKeyTier
    user_id: Optional[str]
    organization: Optional[str]
    contact_email: Optional[str]
    daily_quota: int
    monthly_quota: int
    rate_limit_per_minute: int
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    last_used_at: Optional[datetime]


class CreateAPIKeyResponse(BaseModel):
    api_key_info: APIKeyResponse
    api_key: str = Field(..., description="The actual API key - save this securely!")


class UpdateAPIKeyStatusRequest(BaseModel):
    status: APIKeyStatus
    reason: Optional[str] = Field(None, description="Reason for status change")


class UsageStatsResponse(BaseModel):
    api_key_id: str
    daily_usage: int
    daily_quota: int
    daily_remaining: int
    quota_exceeded: bool
    last_used: Optional[datetime]


# Admin authentication (simple for now - in production, use proper admin auth)
ADMIN_API_KEY = os.getenv("ADMIN_API_KEY", "admin-" + os.getenv("API_KEY", "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"))

def verify_admin_key(x_admin_key: Optional[str] = None):
    """Simple admin authentication"""
    if not x_admin_key or x_admin_key != ADMIN_API_KEY:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Admin API key required"
        )
    return x_admin_key


# Startup and shutdown events
@admin_app.on_event("startup")
async def startup_event():
    """Initialize database connections"""
    await startup_database()


@admin_app.on_event("shutdown")
async def shutdown_event():
    """Clean up database connections"""
    await shutdown_database()


# Health check endpoint
@admin_app.get("/admin/health")
async def health_check():
    """Health check endpoint"""
    db_health = await check_database_health()
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "PropBolt API Key Management",
        **db_health
    }


# API Key Management Endpoints
@admin_app.post("/admin/api-keys", response_model=CreateAPIKeyResponse)
async def create_api_key(
    request: CreateAPIKeyRequest,
    admin_key: str = Depends(verify_admin_key)
):
    """Create a new API key"""
    try:
        api_key_model, actual_key = APIKeyService.create_api_key(
            name=request.name,
            description=request.description,
            tier=request.tier,
            user_id=request.user_id,
            organization=request.organization,
            contact_email=request.contact_email,
            daily_quota=request.daily_quota,
            monthly_quota=request.monthly_quota,
            rate_limit_per_minute=request.rate_limit_per_minute,
            expires_days=request.expires_days,
            is_test_key=request.is_test_key
        )
        
        return CreateAPIKeyResponse(
            api_key_info=APIKeyResponse(
                id=str(api_key_model.id),
                key_id=api_key_model.key_id,
                name=api_key_model.name,
                description=api_key_model.description,
                status=api_key_model.status,
                tier=api_key_model.tier,
                user_id=api_key_model.user_id,
                organization=api_key_model.organization,
                contact_email=api_key_model.contact_email,
                daily_quota=api_key_model.daily_quota,
                monthly_quota=api_key_model.monthly_quota,
                rate_limit_per_minute=api_key_model.rate_limit_per_minute,
                created_at=api_key_model.created_at,
                updated_at=api_key_model.updated_at,
                expires_at=api_key_model.expires_at,
                last_used_at=api_key_model.last_used_at
            ),
            api_key=actual_key
        )
        
    except Exception as e:
        logger.error(f"Failed to create API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create API key: {str(e)}"
        )


@admin_app.get("/admin/api-keys", response_model=List[APIKeyResponse])
async def list_api_keys(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    organization: Optional[str] = Query(None, description="Filter by organization"),
    status_filter: Optional[APIKeyStatus] = Query(None, alias="status", description="Filter by status"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of results"),
    offset: int = Query(0, ge=0, description="Number of results to skip"),
    admin_key: str = Depends(verify_admin_key)
):
    """List API keys with optional filters"""
    try:
        api_keys = APIKeyService.list_api_keys(
            user_id=user_id,
            organization=organization,
            status=status_filter,
            limit=limit,
            offset=offset
        )
        
        return [
            APIKeyResponse(
                id=str(key.id),
                key_id=key.key_id,
                name=key.name,
                description=key.description,
                status=key.status,
                tier=key.tier,
                user_id=key.user_id,
                organization=key.organization,
                contact_email=key.contact_email,
                daily_quota=key.daily_quota,
                monthly_quota=key.monthly_quota,
                rate_limit_per_minute=key.rate_limit_per_minute,
                created_at=key.created_at,
                updated_at=key.updated_at,
                expires_at=key.expires_at,
                last_used_at=key.last_used_at
            )
            for key in api_keys
        ]
        
    except Exception as e:
        logger.error(f"Failed to list API keys: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list API keys: {str(e)}"
        )


@admin_app.get("/admin/api-keys/{key_id}", response_model=APIKeyResponse)
async def get_api_key(
    key_id: str,
    admin_key: str = Depends(verify_admin_key)
):
    """Get details of a specific API key"""
    try:
        api_key = APIKeyService.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
        
        return APIKeyResponse(
            id=str(api_key.id),
            key_id=api_key.key_id,
            name=api_key.name,
            description=api_key.description,
            status=api_key.status,
            tier=api_key.tier,
            user_id=api_key.user_id,
            organization=api_key.organization,
            contact_email=api_key.contact_email,
            daily_quota=api_key.daily_quota,
            monthly_quota=api_key.monthly_quota,
            rate_limit_per_minute=api_key.rate_limit_per_minute,
            created_at=api_key.created_at,
            updated_at=api_key.updated_at,
            expires_at=api_key.expires_at,
            last_used_at=api_key.last_used_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get API key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get API key: {str(e)}"
        )


@admin_app.patch("/admin/api-keys/{key_id}/status")
async def update_api_key_status(
    key_id: str,
    request: UpdateAPIKeyStatusRequest,
    admin_key: str = Depends(verify_admin_key)
):
    """Update API key status"""
    try:
        success = APIKeyService.update_api_key_status(
            key_id=key_id,
            status=request.status,
            reason=request.reason
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
        
        return {"message": f"API key status updated to {request.status.value}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update API key status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update API key status: {str(e)}"
        )


@admin_app.get("/admin/api-keys/{key_id}/usage", response_model=UsageStatsResponse)
async def get_api_key_usage(
    key_id: str,
    admin_key: str = Depends(verify_admin_key)
):
    """Get usage statistics for an API key"""
    try:
        api_key = APIKeyService.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API key not found"
            )
        
        quota_info = UsageTrackingService.check_quota_exceeded(api_key)
        
        return UsageStatsResponse(
            api_key_id=str(api_key.id),
            daily_usage=quota_info["daily_usage"],
            daily_quota=quota_info["daily_quota"],
            daily_remaining=quota_info["daily_remaining"],
            quota_exceeded=quota_info["daily_quota_exceeded"],
            last_used=api_key.last_used_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get API key usage: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get API key usage: {str(e)}"
        )


if __name__ == "__main__":
    port = int(os.getenv("ADMIN_PORT", 8001))
    print("🔧 Starting PropBolt API Key Management Admin Interface...")
    print(f"📍 Admin interface: http://localhost:{port}/admin/docs")
    print(f"🔑 Admin API Key: {ADMIN_API_KEY}")
    print("⏹️  Press Ctrl+C to stop the server\n")
    
    uvicorn.run("admin_api:admin_app", host="0.0.0.0", port=port, reload=False)
