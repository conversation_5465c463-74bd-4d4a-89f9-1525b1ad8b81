# PropBolt API Key Management System

## Overview

The PropBolt API now includes a comprehensive PostgreSQL-based API key management system for data.propbolt.com. This system provides:

- **Database-driven API key authentication**
- **Usage tracking and quota management**
- **Rate limiting and monitoring**
- **Admin interface for key management**
- **Backward compatibility** with existing `getenv` approach

## Architecture

### Database Components
- **PostgreSQL Database**: `gold-braid-458901-v2:us-central1:propbolt-postgres`
- **Connection**: `*************:5432`
- **Tables**: `api_keys`, `api_usage_logs`, `daily_usage_summaries`, `api_key_events`

### Key Features
1. **Dual Authentication**: Database keys + fallback AYO key
2. **Sync/Async Operations**: Cost-optimized database operations
3. **Auto-scaling**: Compute Engine + App Engine integration
4. **Comprehensive Logging**: Request tracking and analytics

## Database Schema

### API Keys Table
```sql
- id (UUID, Primary Key)
- key_id (String, Unique Index)
- key_hash (SHA-256 hash)
- key_prefix (pb_live_/pb_test_)
- name, description
- status (active/suspended/revoked/expired)
- tier (free/basic/professional/enterprise/unlimited)
- quotas (daily/monthly limits)
- timestamps (created/updated/expires/last_used)
```

### Usage Tracking
```sql
- Detailed request logs (endpoint, method, status, timing)
- Daily usage summaries (for efficient quota checking)
- Performance metrics (response times, data transfer)
- Error tracking and debugging information
```

## Setup Instructions

### 1. Database Migration
```bash
# Run the migration script
python database/migrations.py

# This will:
# - Create all necessary tables
# - Set up indexes for performance
# - Optionally create default API keys
```

### 2. Environment Configuration
Update your `app.yaml` or environment variables:

```yaml
env_variables:
  # Existing (maintained for backward compatibility)
  API_KEY: "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
  
  # Database Configuration
  DB_HOST: "*************"
  DB_PORT: "5432"
  DB_NAME: "propbolt"
  DB_USER: "postgres"
  DB_PASSWORD: "your_secure_password"
  DB_SSL_MODE: "require"
  
  # Admin Access
  ADMIN_API_KEY: "admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914"
```

### 3. Deploy Application
```bash
# Deploy main API
gcloud app deploy app.yaml

# Deploy admin interface (optional)
gcloud app deploy admin.yaml --service=admin
```

## API Key Types

### 1. Database-Managed Keys
- **Format**: `pb_live_[64-char-hex]` or `pb_test_[64-char-hex]`
- **Features**: Full quota management, usage tracking, rate limiting
- **Tiers**: Free, Basic, Professional, Enterprise, Unlimited

### 2. Fallback AYO Key
- **Key**: `AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914`
- **Purpose**: Backward compatibility and emergency access
- **Features**: No quota limits, basic logging

## Usage Examples

### Creating API Keys (Admin Interface)
```bash
# Access admin interface
curl -X POST "https://data.propbolt.com/admin/api-keys" \
  -H "x-admin-key: admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Customer API Key",
    "description": "Production key for customer XYZ",
    "tier": "professional",
    "organization": "Customer XYZ",
    "contact_email": "<EMAIL>",
    "daily_quota": 50000,
    "monthly_quota": 1000000
  }'
```

### Using API Keys
```bash
# Standard API call with database-managed key
curl -X POST "https://data.propbolt.com/v2/PropertyDetail" \
  -H "x-api-key: pb_live_abc123..." \
  -H "Content-Type: application/json" \
  -d '{"address": "123 Main St, Anytown, CA 90210"}'

# Fallback with AYO key (backward compatibility)
curl -X POST "https://data.propbolt.com/v2/PropertyDetail" \
  -H "x-api-key: AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914" \
  -H "Content-Type: application/json" \
  -d '{"address": "123 Main St, Anytown, CA 90210"}'
```

## Admin Interface

### Endpoints
- `GET /admin/health` - System health check
- `POST /admin/api-keys` - Create new API key
- `GET /admin/api-keys` - List API keys (with filters)
- `GET /admin/api-keys/{key_id}` - Get specific key details
- `PATCH /admin/api-keys/{key_id}/status` - Update key status
- `GET /admin/api-keys/{key_id}/usage` - Get usage statistics

### Access
- **URL**: `https://data.propbolt.com/admin/docs`
- **Authentication**: Admin API key in `x-admin-key` header
- **Key**: `admin-AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914`

## Monitoring & Analytics

### Health Check
```bash
curl "https://data.propbolt.com/health"
```

Response includes:
- Database connection status
- API key management mode (database/fallback)
- SDK client availability
- Overall system health

### Usage Tracking
- Real-time request logging
- Daily usage summaries
- Performance metrics
- Error tracking
- Quota monitoring

## Rate Limiting

### Headers
API responses include rate limit information:
```
X-RateLimit-Limit: 50000
X-RateLimit-Remaining: 49999
X-RateLimit-Reset: **********
```

### Quota Exceeded Response
```json
{
  "error": "Quota exceeded",
  "message": "Daily API quota exceeded",
  "quota_info": {
    "daily_quota_exceeded": true,
    "daily_usage": 50000,
    "daily_quota": 50000,
    "daily_remaining": 0
  }
}
```

## Security Features

### Key Storage
- Keys are SHA-256 hashed in database
- Only key prefixes stored for identification
- Secure key generation using `secrets` module

### Access Control
- IP address logging
- User agent tracking
- Admin-only management endpoints
- Audit trail for all key operations

### SSL/TLS
- Required SSL connections to Cloud SQL
- HTTPS-only API endpoints
- Secure header transmission

## Troubleshooting

### Database Connection Issues
1. Check environment variables
2. Verify Cloud SQL instance status
3. Confirm IP whitelist settings
4. Test connection with health endpoint

### Fallback Mode
If database is unavailable:
- System automatically falls back to AYO key
- Health endpoint shows "fallback" mode
- No usage tracking during fallback

### Common Issues
- **401 Unauthorized**: Invalid or missing API key
- **429 Too Many Requests**: Quota exceeded
- **500 Internal Error**: Database connection issues

## Cost Optimization

### Sync vs Async Operations
- **Sync**: Used for authentication (fast response required)
- **Async**: Used for usage logging (non-blocking)
- **Connection Pooling**: Efficient resource usage

### Auto-scaling
- Compute Engine handles traffic spikes
- App Engine provides cost-effective baseline
- Database connection pooling prevents overload

## Migration Guide

### From Existing Setup
1. **No immediate changes required** - AYO key continues to work
2. **Run database migration** when ready
3. **Create new database keys** for customers
4. **Gradually migrate** customers to new keys
5. **Monitor usage** through admin interface

### Testing
1. Use test keys (`pb_test_*`) for development
2. Verify quota enforcement
3. Test fallback behavior
4. Monitor performance impact

## Support

For issues or questions:
1. Check health endpoint: `/health`
2. Review admin interface: `/admin/docs`
3. Check database logs
4. Verify environment configuration
